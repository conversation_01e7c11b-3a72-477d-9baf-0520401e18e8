{"remainingRequest": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Desktop\\XM\\FW\\baogang\\browser\\src\\view\\Compliance\\Management\\ComplianceOperation\\ComplianceReport\\HgscTz.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\src\\view\\Compliance\\Management\\ComplianceOperation\\ComplianceReport\\HgscTz.vue", "mtime": 1756905062235}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\r\n// 组件\r\nimport pagination from '@/view/components/Pagination/PaginationIndex';\r\nimport TableTools from '@/view/components/TableTools/index';\r\nimport SimpleBoardIndex from '@/view/components/SimpleBoard/SimpleBoardIndex';\r\nimport orgTree from '@/view/components/OrgTree/OrgTree';\r\n// vuex审查状态值\r\nimport { mapGetters } from 'vuex';\r\n\r\n// 接口api\r\nimport complianceReviewApi from '@/api/ComplianceReview/complianceReview.js';\r\nimport taskApi from '@/api/_system/task';\r\n\r\nexport default {\r\n  name: 'HgscIndex',\r\n  inject: ['layout'],\r\n  components: { pagination, TableTools, SimpleBoardIndex, orgTree },\r\n  data() {\r\n    return {\r\n      isCheckedUser: false,\r\n      showUser: false,\r\n      deptOrgVisible: false,\r\n      entrustedUnitOrgVisible: false,\r\n      // is_Check: false,\r\n      tableQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        total: 0,\r\n        reviewSubject: '', // 事项题目\r\n        createOgnName: '', // 送审单位\r\n        reviewCategory: '', // 审查分类\r\n        reviewCategoryName: '', // 审查分类\r\n        submitter: '', // 送审人\r\n        submissionDateStart: '', // 送审开始时间\r\n        submissionDateEnd: '', // 送审结束时间\r\n        fuzzyValue: '', // 模糊搜索值\r\n        dataState:'',//台账专用---不等于该值\r\n        isQuery:true,//是否查询台账\r\n        orgId: '', // 上报单位id\r\n        businessArea:'',//业务领域\r\n      },\r\n      table_height: '100%',\r\n      tableData: [],\r\n      userDialogVisible: false,\r\n      orgDialogVisible: false,\r\n      orgTreeDialog: false,\r\n      zxcheckedData: [],\r\n      orgVisible: false,\r\n      tableLoading: false,\r\n      ss: {\r\n        data: this.tableData,\r\n        tableColumns: [\r\n          { key: 'reviewSubject', label: '事项题目', visible: true },\r\n          { key: 'reviewCategory', label: '审查分类', visible: true },\r\n          { key: 'reviewCategoryName', label: '审查分类', visible: true },\r\n          { key: 'submissionDate', label: '送审时间', visible: true },\r\n          { key: 'submitter', label: '送审人', visible: true },\r\n          { key: 'createOgnName', label: '送审单位', visible: true },\r\n          { key: 'dataState', label: '审查状态', visible: true },\r\n          { key: 'businessArea', label: '业务领域', visible: true },\r\n        ],\r\n      },\r\n      // tableQuery: {\r\n      //   reportYear: '',\r\n      // },\r\n      yearOptions: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i),\r\n      businessAreaData: [],\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapGetters(['orgContext', 'currentFunctionId']),\r\n  },\r\n  activated() {\r\n    // 长连接页面第二次激活的时候,不会走created方法,会走此方法\r\n    this.refreshData();\r\n  },\r\n  created() {\r\n    this.refreshData();\r\n    this.initDic();\r\n  },\r\n\r\n  mounted() {\r\n    this.$nextTick(function () {\r\n      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45;\r\n\r\n      // 监听窗口大小变化\r\n      const self = this;\r\n      window.onresize = function () {\r\n        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45;\r\n      };\r\n    });\r\n  },\r\n  methods: {\r\n        initDic() {\r\n      const code = ['businessDomainDic'];\r\n      this.utils.getDic(code).then((response) => {\r\n        this.businessAreaData = response.data.data[code[0]];\r\n      })\r\n    },\r\n    isEdit(row) {\r\n      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataStateCode === this.utils.dataState_BPM.BACK.code;\r\n    },\r\n    isDelete(row) {\r\n      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code;\r\n    },\r\n    // 刷新数据\r\n    refreshData() {\r\n      // 赋值当前人组织全路径\r\n      this.tableQuery.functionCode = this.currentFunctionId.functionCode;\r\n      this.tableQuery.orgId = this.orgContext.currentOrgId;\r\n      this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId;\r\n      // 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页\r\n      if (this.tableData.length === 0 && this.tableQuery.page > 1) {\r\n        this.tableQuery.page--;\r\n      }\r\n      complianceReviewApi\r\n        .query(this.tableQuery)\r\n        .then((response) => {\r\n          let rows = response.data.data.records;\r\n          this.tableData = rows;\r\n          this.ss.data = rows;\r\n          this.tableQuery.total = response.data.data.total;\r\n          this.tableLoading = false;\r\n        })\r\n        .catch({});\r\n    },\r\n    // 点击打开查找机构弹窗\r\n    chooseNoticeDeptClick() {\r\n      this.deptOrgVisible = true;\r\n    },\r\n    // 关闭审查机构弹窗\r\n    deptOrgCancel() {\r\n      this.deptOrgVisible = false;\r\n    },\r\n    // 点击打开送审人弹窗\r\n    choiceEntrustedUnitClick() {\r\n      this.entrustedUnitOrgVisible = true;\r\n    },\r\n    // 点击关闭送审人弹窗\r\n    entrustedUnitOrgCancel() {\r\n      this.entrustedUnitOrgVisible = false;\r\n    },\r\n    entrustedUnitSure() {\r\n      const res = this.zxcheckedData[0];\r\n      console.log(res, 'res');\r\n      this.tableQuery.submitter = res.name;\r\n      this.entrustedUnitOrgVisible = false;\r\n    },\r\n    choiceNoticeDeptSure() {\r\n      let c = '';\r\n      let cid = '';\r\n      this.zxcheckedData.forEach((item) => {\r\n        if (c.length === 0) {\r\n          c = c + item.name;\r\n          cid = cid + item.unitId;\r\n        } else {\r\n          c = c + ',' + item.name;\r\n          cid = cid + ',' + item.unitId;\r\n        }\r\n      });\r\n      this.tableQuery.createOgnName = c;\r\n      this.deptOrgVisible = false;\r\n    },\r\n    add_(event) {\r\n      let tableName;\r\n      const name = this.utils.getDicName(this.utils.compliance_review_type, event);\r\n      const tabId = this.utils.createUUID();\r\n      console.log('name:', name);\r\n      console.log('name.id:', name?.id);\r\n      console.log('event:', event);\r\n      if (name ==='重大决策事项合规审查'){\r\n         tableName='hgsc_main_detail';\r\n      }else if (name ==='重要请示事项合规审查'){\r\n         tableName='hgsc_main_detail_qsss';\r\n      }\r\n      else if (name ==='内部规章制度合法合规审查'){\r\n         tableName='hgsc_main_detail_zdhf';\r\n      }\r\n      else if (name ==='特殊经营类合规论证'){\r\n         tableName='hgsc_main_detail_tsjy';\r\n      }\r\n      else if (name ==='合法合规审查意见'){\r\n         tableName='hgsc_main_detail_hfhg';\r\n      }\r\n\r\n      this.layout.openNewTab(name, tableName, tableName, tabId, {\r\n        functionId: tableName +',' + tabId,\r\n        ...this.utils.routeState.NEW(tabId),\r\n        reviewCategory: event, reviewCategoryName: name,\r\n      });\r\n    },\r\n    // 编辑\r\n    edit_(index, row) {\r\n      let tableName;\r\n      if (row.reviewCategoryName ==='重大决策事项合规审查'){\r\n        tableName='hgsc_main_detail';\r\n      }else if (row.reviewCategoryName ==='重要请示事项合规审查'){\r\n        tableName='hgsc_main_detail_qsss';\r\n      }\r\n      else if (row.reviewCategoryName ==='内部规章制度合法合规审查'){\r\n        tableName='hgsc_main_detail_zdhf';\r\n      }\r\n      else if (row.reviewCategoryName ==='特殊经营类合规论证'){\r\n        tableName='hgsc_main_detail_tsjy';\r\n      }\r\n      else if (row.reviewCategoryName ==='合法合规审查意见'){\r\n        tableName='hgsc_main_detail_hfhg';\r\n      }\r\n      console.log(row, 'row');\r\n      const tabId = this.utils.createUUID();\r\n      this.layout.openNewTab(row.reviewCategoryName, tableName, tableName, row.id, {\r\n        functionId: tableName+','  + row.id,\r\n        ...this.utils.routeState.EDIT(row.id),\r\n        view: 'old',\r\n        reviewCategory: row.reviewCategory, reviewCategoryName: row.reviewCategoryName\r\n      });\r\n    },\r\n    // 删除\r\n    delete_(index, row) {\r\n      this.$confirm('此操作将永久删除该数据及相关数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      })\r\n        .then(() => {\r\n          new Promise((resolve, reject) => {\r\n            complianceReviewApi\r\n              .deletebyid({\r\n                id: row.id,\r\n              })\r\n              .then((response) => {\r\n                resolve(response);\r\n              });\r\n          }).then((value) => {\r\n            this.tableData.splice(index, 1);\r\n            this.$message.success('删除成功!');\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除',\r\n          });\r\n        });\r\n    },\r\n    // 查看\r\n    view_(index, row) {\r\n      let tableName;\r\n      if (row.reviewCategoryName ==='重大决策事项合规审查'){\r\n        tableName='hgsc_main_detail';\r\n      }else if (row.reviewCategoryName ==='重要请示事项合规审查'){\r\n        tableName='hgsc_main_detail_qsss';\r\n      }\r\n      else if (row.reviewCategoryName ==='内部规章制度合法合规审查'){\r\n        tableName='hgsc_main_detail_zdhf';\r\n      }\r\n      else if (row.reviewCategoryName ==='特殊经营类合规论证'){\r\n        tableName='hgsc_main_detail_tsjy';\r\n      }\r\n      else if (row.reviewCategoryName ==='合法合规审查意见'){\r\n        tableName='hgsc_main_detail_hfhg';\r\n      }\r\n      if (row.dataStateCode == this.utils.dataState_BPM.SAVE.code) {\r\n        const tabId = this.utils.createUUID();\r\n        this.layout.openNewTab('合同合规审查', tableName, tableName, tabId, {\r\n          functionId: tableName+',' + tabId,\r\n          ...this.utils.routeState.VIEW(row.id),\r\n          reviewCategoryName: row.reviewCategoryName\r\n        });\r\n      } else {\r\n        taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then((res) => {\r\n          const functionId = res.data.data[0].ID;\r\n          const tabId = this.utils.createUUID();\r\n          this.layout.openNewTab('合规审查信息', 'design_page', 'design_page', tabId, {\r\n            processInstanceId: res.data.data[0].PID, //流程实例\r\n            taskId: res.data.data[0].ID, //任务ID\r\n            businessKey: row.id, //业务数据ID\r\n            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了\r\n            entranceType: 'FLOWABLE',\r\n            type: 'haveDealt',\r\n            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮\r\n            view: 'new',\r\n            reviewCategoryName: row.reviewCategoryName\r\n          });\r\n        });\r\n      }\r\n    },\r\n    rowDblclick(row, column, event) {\r\n      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code) {\r\n        const tabId = this.utils.createUUID();\r\n        this.layout.openNewTab('合规报告审批信息', tableName, tableName, tabId, {\r\n          functionId: tableName+',' + tabId,\r\n          ...this.utils.routeState.VIEW(row.id),\r\n        });\r\n      } else {\r\n        taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then((res) => {\r\n          const functionId = res.data.data[0].ID;\r\n          const tabId = this.utils.createUUID();\r\n          this.layout.openNewTab('合规报告审批信息', 'design_page', 'design_page', tabId, {\r\n            processInstanceId: res.data.data[0].PID, //流程实例\r\n            taskId: res.data.data[0].ID, //任务ID\r\n            businessKey: row.id, //业务数据ID\r\n            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了\r\n            entranceType: 'FLOWABLE',\r\n            type: 'haveDealt',\r\n            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮\r\n            view: 'new',\r\n          });\r\n        });\r\n      }\r\n    },\r\n    tableSort(column, prop, order) {\r\n      this.tableQuery.sortName = column.prop;\r\n      this.tableQuery.order = column.order === 'ascending';\r\n      this.refreshData();\r\n    },\r\n    // 点击搜索按钮事件,回到第一页,重新刷新数据\r\n    search_: function () {\r\n      this.tableQuery.page = 1;\r\n      this.refreshData();\r\n    },\r\n    // 点击清空按钮事件,清空查询条件属性,回到第一页,重新刷新数据\r\n    empty_() {\r\n      // 清空搜索条件\r\n      this.tableQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        total: 0,\r\n        riskName: '',\r\n        caseCode: '',\r\n        expectedRiskLevel: '',\r\n        fuzzyValue: '',\r\n        isQuery: true,\r\n      };\r\n      this.refreshData();\r\n    },\r\n    // 点击刷新按钮事件\r\n    refresh_() {\r\n      this.tableQuery.sortName = null;\r\n      this.tableQuery.order = null;\r\n      this.empty_();\r\n    },\r\n    cancel() {\r\n      this.userDialogVisible = false;\r\n    },\r\n    choiceDeptSure() {\r\n      let selectedUnits = this.zxcheckedData.map((item) => item.name).join(', ');\r\n      this.tableQuery.reportingUnit = selectedUnits;\r\n      this.userDialogVisible = false;\r\n    },\r\n        exportExcel() {\r\n      this.exportLoading = true;\r\n\r\n      // 显示提示消息\r\n      const loadingMessage = this.$message({\r\n        message: '正在准备导出数据，请稍候...',\r\n        type: 'info',\r\n        duration: 0\r\n      });\r\n\r\n      const queryParams = {};\r\n      Object.assign(queryParams, this.tableQuery);\r\n      queryParams.limit = 9999;\r\n\r\n      let date = new Date();\r\n      let formatDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();\r\n\r\n      complianceReviewApi.exportRiskLedger(queryParams).then((response) => {\r\n        const blob = response.data;\r\n        const fileName = formatDate + '合规审查台账.xlsx';\r\n\r\n        // 关闭提示消息\r\n        loadingMessage.close();\r\n\r\n        // 下载文件\r\n        if ('download' in document.createElement('a')) {\r\n          const elink = document.createElement('a');\r\n          elink.download = fileName;\r\n          elink.style.display = 'none';\r\n          elink.href = URL.createObjectURL(blob);\r\n          document.body.appendChild(elink);\r\n          elink.click();\r\n          URL.revokeObjectURL(elink.href);\r\n          document.body.removeChild(elink);\r\n\r\n          this.$message.success('导出成功');\r\n        } else {\r\n          navigator.msSaveBlob(blob, fileName);\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('导出失败：' + (error.message || '未知错误'));\r\n      }).finally(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n  },\r\n};\r\n", {"version": 3, "sources": ["HgscTz.vue"], "names": [], "mappings": ";AAuLA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "HgscTz.vue", "sourceRoot": "src/view/Compliance/Management/ComplianceOperation/ComplianceReport", "sourcesContent": ["<template>\r\n  <el-container class=\"container-manage-sg\" direction=\"vertical\">\r\n    <el-header>\r\n      <el-card>\r\n        <div>\r\n          <el-input v-model=\"tableQuery.fuzzyValue\" class=\"filter_input\" clearable placeholder=\"检索字段（事项题目、审查分类、送审部门）\"\r\n            @clear=\"refreshData\" @keyup.enter.native=\"refreshData\">\r\n            <el-popover slot=\"prepend\" placement=\"bottom-start\" trigger=\"click\" width=\"1000\">\r\n              <el-form ref=\"queryForm\" label-width=\"100px\" size=\"mini\">\r\n                <!-- 选择部门弹窗 -->\r\n                <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"deptOrgVisible\" title=\"选择部门\" width=\"50%\">\r\n                  <div class=\"el-dialog-div\" style=\"z-index: 999999999\">\r\n                    <orgTree :accordion=\"false\" :checked-data.sync=\"zxcheckedData\" :is-check=\"true\"\r\n                      :is-checked-user=\"false\" :is-filter=\"true\" :is-not-cascade=\"true\" :show-user=\"false\" />\r\n                  </div>\r\n                  <span slot=\"footer\" class=\"dialog-footer\">\r\n                    <el-button class=\"negative-btn\" icon=\"\" @click=\"deptOrgCancel\">取消</el-button>\r\n                    <el-button class=\"active-btn\" icon=\"\" type=\"primary\" @click=\"choiceNoticeDeptSure\">确定</el-button>\r\n                  </span>\r\n                </el-dialog>\r\n                <!-- 选择送审人弹窗 -->\r\n                <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"entrustedUnitOrgVisible\" title=\"选择送审人\"\r\n                  width=\"50%\">\r\n                  <div class=\"el-dialog-div\">\r\n                    <orgTree :accordion=\"false\" :checked-data.sync=\"zxcheckedData\" :is-check=\"false\"\r\n                      :is-checked-user=\"false\" :is-filter=\"true\" :is-not-cascade=\"true\" :show-user=\"true\" />\r\n                  </div>\r\n                  <span slot=\"footer\" class=\"dialog-footer\">\r\n                    <el-button class=\"negative-btn\" icon=\"\" @click=\"entrustedUnitOrgCancel\">取消</el-button>\r\n                    <el-button class=\"active-btn\" icon=\"\" type=\"primary\" @click=\"entrustedUnitSure\">确定</el-button>\r\n                  </span>\r\n                </el-dialog>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"送审主题\">\r\n                      <el-input v-model=\"tableQuery.reviewSubject\" clearable placeholder=\"请输入...\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"送审分类\">\r\n                      <el-select v-model=\"tableQuery.reviewCategory\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n                        <el-option v-for=\"item in utils.compliance_report_type\" :key=\"item.dicName\"\r\n                          :label=\"item.dicName\" :value=\"item.dicName\" />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                  <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"审查状态\">\r\n                      <el-input v-model=\"tableQuery.dataState\" clearable placeholder=\"请输入...\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"业务领域\" >\r\n                    <el-select v-model=\"tableQuery.businessArea\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n                      <el-option v-for=\"item in businessAreaData\" :key=\"item.dicName\" :label=\"item.dicName\"\r\n                        :value=\"item.dicName\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <!-- <el-form-item label=\"送审部门\">\r\n                      <el-input v-model=\"tableQuery.createOgnName\" placeholder=\"请选择\" class=\"input-with-select\">\r\n                        <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"showOrgTreeDialog\"/>\r\n                      </el-input>\r\n                    </el-form-item> -->\r\n                    <el-form-item label=\"送审部门\" prop=\"createOgnName\">\r\n                      <el-input v-model=\"tableQuery.createOgnName\" class=\"input-with-select\" clearable\r\n                        placeholder=\"请选择\">\r\n                        <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"chooseNoticeDeptClick\" />\r\n                      </el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"送审人\">\r\n                      <el-input v-model=\"tableQuery.submitter\" class=\"input-with-select\" clearable placeholder=\"请选择\">\r\n                        <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"choiceEntrustedUnitClick\" />\r\n                      </el-input>\r\n                      <!-- <el-input v-model=\"tableQuery.submitter\" placeholder=\"请选择\" class=\"input-with-select\">\r\n                        <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"showUserTreeDialog\"/>\r\n                      </el-input> -->\r\n                      <!-- <span v-else class=\"viewSpan\">{{ mainData.riskDepartment }}</span> -->\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"送审时间\">\r\n                      <el-date-picker v-model=\"tableQuery.submissionDateStart\" clearable placeholder=\"选择日期\"\r\n                        style=\"width: 45%; float: left\" type=\"date\" />\r\n                      <div class=\"label_1\" style=\"width: 10%; float: left; text-align: center\"><span>至</span></div>\r\n                      <el-date-picker v-model=\"tableQuery.submissionDateEnd\" clearable placeholder=\"选择日期\"\r\n                        style=\"width: 45%\" type=\"date\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-button-group style=\"float: right\">\r\n                  <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"search_\">搜索</el-button>\r\n                  <el-button icon=\"el-icon-refresh-left\" size=\"mini\" type=\"primary\" @click=\"empty_\">重置</el-button>\r\n                </el-button-group>\r\n              </el-form>\r\n              <!-- el-dialog 组件 -->\r\n              <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"userDialogVisible\" title=\"选择送审人\" width=\"50%\">\r\n                <div class=\"el-dialog-div\">\r\n                  <orgTree :accordion=\"false\" :checked-data.sync=\"zxcheckedData\" :is-check=\"true\"\r\n                    :is-checked-user=\"isCheckedUser\" :is-filter=\"true\" :is-not-cascade=\"true\" :show-user=\"showUser\" />\r\n                </div>\r\n                <span slot=\"footer\" class=\"dialog-footer\">\r\n                  <el-button class=\"negative-btn\" icon=\"\" @click=\"cancel\">取消</el-button>\r\n                  <el-button class=\"active-btn\" icon=\"\" type=\"primary\" @click=\"choiceDeptSure\">确定</el-button>\r\n                </span>\r\n              </el-dialog>\r\n              <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"orgDialogVisible\" title=\"选择送审部门\" width=\"50%\">\r\n                <div class=\"el-dialog-div\">\r\n                  <orgTree :accordion=\"false\" :checked-data.sync=\"zxcheckedData\" :is-check=\"true\"\r\n                    :is-checked-user=\"false\" :is-filter=\"true\" :is-not-cascade=\"true\" :show-user=\"false\" />\r\n                </div>\r\n                <span slot=\"footer\" class=\"dialog-footer\">\r\n                  <el-button class=\"negative-btn\" icon=\"\" @click=\"cancel\">取消</el-button>\r\n                  <el-button class=\"active-btn\" icon=\"\" type=\"primary\" @click=\"choiceDeptSure\">确定</el-button>\r\n                </span>\r\n              </el-dialog>\r\n              <el-button slot=\"reference\" size=\"small\" type=\"primary\">高级检索</el-button>\r\n            </el-popover>\r\n            <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"search_\" />\r\n          </el-input>\r\n        </div>\r\n      </el-card>\r\n    </el-header>\r\n\r\n    <el-main>\r\n      <!-- 选择送审单位 -->\r\n      <SimpleBoardIndex :title=\"'合规审查台账'\">\r\n        <template slot=\"button\">\r\n          <el-button class=\"normal-btn\" size=\"mini\" type=\"primary\" @click=\"exportExcel\">导出Excel</el-button>\r\n        </template>\r\n        <el-table ref=\"table\" v-loading=\"tableLoading\" :data=\"tableData\" :height=\"table_height\"\r\n          :show-overflow-tooltip=\"true\" border fit highlight-current-row row-key=\"id\" size=\"mini\" stripe\r\n          style=\"table-layout: fixed; width: 100%\" @sort-change=\"tableSort\" @row-dblclick=\"rowDblclick\">\r\n          <el-table-column align=\"center\" label=\"序号\" type=\"index\" width=\"50\" />\r\n          <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'reviewSubject').visible\" label=\"事项题目\"\r\n            min-width=\"250\" prop=\"reviewSubject\" show-overflow-tooltip />\r\n          <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'reviewCategoryName').visible\" label=\"审查分类\"\r\n            min-width=\"180\" prop=\"reviewCategoryName\" show-overflow-tooltip sortable=\"custom\" />\r\n            <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'businessArea').visible\" label=\"业务领域\"\r\n            min-width=\"120\" prop=\"businessArea\" show-overflow-tooltip />\r\n          <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'submitter').visible\" label=\"送审人\"\r\n            min-width=\"100\" prop=\"submitter\" show-overflow-tooltip />\r\n          <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'createOgnName').visible\" label=\"送审单位\"\r\n            min-width=\"250\" prop=\"createOgnName\" show-overflow-tooltip />\r\n          <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'submissionDate').visible\" label=\"送审时间\"\r\n            min-width=\"100\" prop=\"submissionDate\" show-overflow-tooltip sortable=\"custom\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.submissionDate | parseTime('{y}-{m}-{d}') }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"审查状态\" min-width=\"100\" prop=\"dataState\" show-overflow-tooltip sortable=\"custom\" />\r\n          <el-table-column align=\"center\" fixed=\"right\" label=\"操作\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-button v-if=\"scope.row.dataState == '已保存'\" type=\"text\" @click=\"edit_(scope.$index, scope.row)\">\r\n                编辑\r\n              </el-button> -->\r\n              <el-button type=\"text\" @click=\"view_(scope.$index, scope.row)\">查看</el-button>\r\n              <!-- <el-button v-if=\"scope.row.dataState == '已保存'\" type=\"text\" @click=\"delete_(scope.$index, scope.row)\">\r\n                删除\r\n              </el-button> -->\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </SimpleBoardIndex>\r\n    </el-main>\r\n    <el-footer>\r\n      <!--分页工具栏-->\r\n      <pagination :limit.sync=\"tableQuery.limit\" :page.sync=\"tableQuery.page\" :total=\"tableQuery.total\"\r\n        @pagination=\"refreshData\" />\r\n    </el-footer>\r\n  </el-container>\r\n</template>\r\n\r\n<script>\r\n// 组件\r\nimport pagination from '@/view/components/Pagination/PaginationIndex';\r\nimport TableTools from '@/view/components/TableTools/index';\r\nimport SimpleBoardIndex from '@/view/components/SimpleBoard/SimpleBoardIndex';\r\nimport orgTree from '@/view/components/OrgTree/OrgTree';\r\n// vuex审查状态值\r\nimport { mapGetters } from 'vuex';\r\n\r\n// 接口api\r\nimport complianceReviewApi from '@/api/ComplianceReview/complianceReview.js';\r\nimport taskApi from '@/api/_system/task';\r\n\r\nexport default {\r\n  name: 'HgscIndex',\r\n  inject: ['layout'],\r\n  components: { pagination, TableTools, SimpleBoardIndex, orgTree },\r\n  data() {\r\n    return {\r\n      isCheckedUser: false,\r\n      showUser: false,\r\n      deptOrgVisible: false,\r\n      entrustedUnitOrgVisible: false,\r\n      // is_Check: false,\r\n      tableQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        total: 0,\r\n        reviewSubject: '', // 事项题目\r\n        createOgnName: '', // 送审单位\r\n        reviewCategory: '', // 审查分类\r\n        reviewCategoryName: '', // 审查分类\r\n        submitter: '', // 送审人\r\n        submissionDateStart: '', // 送审开始时间\r\n        submissionDateEnd: '', // 送审结束时间\r\n        fuzzyValue: '', // 模糊搜索值\r\n        dataState:'',//台账专用---不等于该值\r\n        isQuery:true,//是否查询台账\r\n        orgId: '', // 上报单位id\r\n        businessArea:'',//业务领域\r\n      },\r\n      table_height: '100%',\r\n      tableData: [],\r\n      userDialogVisible: false,\r\n      orgDialogVisible: false,\r\n      orgTreeDialog: false,\r\n      zxcheckedData: [],\r\n      orgVisible: false,\r\n      tableLoading: false,\r\n      ss: {\r\n        data: this.tableData,\r\n        tableColumns: [\r\n          { key: 'reviewSubject', label: '事项题目', visible: true },\r\n          { key: 'reviewCategory', label: '审查分类', visible: true },\r\n          { key: 'reviewCategoryName', label: '审查分类', visible: true },\r\n          { key: 'submissionDate', label: '送审时间', visible: true },\r\n          { key: 'submitter', label: '送审人', visible: true },\r\n          { key: 'createOgnName', label: '送审单位', visible: true },\r\n          { key: 'dataState', label: '审查状态', visible: true },\r\n          { key: 'businessArea', label: '业务领域', visible: true },\r\n        ],\r\n      },\r\n      // tableQuery: {\r\n      //   reportYear: '',\r\n      // },\r\n      yearOptions: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i),\r\n      businessAreaData: [],\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapGetters(['orgContext', 'currentFunctionId']),\r\n  },\r\n  activated() {\r\n    // 长连接页面第二次激活的时候,不会走created方法,会走此方法\r\n    this.refreshData();\r\n  },\r\n  created() {\r\n    this.refreshData();\r\n    this.initDic();\r\n  },\r\n\r\n  mounted() {\r\n    this.$nextTick(function () {\r\n      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45;\r\n\r\n      // 监听窗口大小变化\r\n      const self = this;\r\n      window.onresize = function () {\r\n        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45;\r\n      };\r\n    });\r\n  },\r\n  methods: {\r\n        initDic() {\r\n      const code = ['businessDomainDic'];\r\n      this.utils.getDic(code).then((response) => {\r\n        this.businessAreaData = response.data.data[code[0]];\r\n      })\r\n    },\r\n    isEdit(row) {\r\n      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataStateCode === this.utils.dataState_BPM.BACK.code;\r\n    },\r\n    isDelete(row) {\r\n      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code;\r\n    },\r\n    // 刷新数据\r\n    refreshData() {\r\n      // 赋值当前人组织全路径\r\n      this.tableQuery.functionCode = this.currentFunctionId.functionCode;\r\n      this.tableQuery.orgId = this.orgContext.currentOrgId;\r\n      this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId;\r\n      // 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页\r\n      if (this.tableData.length === 0 && this.tableQuery.page > 1) {\r\n        this.tableQuery.page--;\r\n      }\r\n      complianceReviewApi\r\n        .query(this.tableQuery)\r\n        .then((response) => {\r\n          let rows = response.data.data.records;\r\n          this.tableData = rows;\r\n          this.ss.data = rows;\r\n          this.tableQuery.total = response.data.data.total;\r\n          this.tableLoading = false;\r\n        })\r\n        .catch({});\r\n    },\r\n    // 点击打开查找机构弹窗\r\n    chooseNoticeDeptClick() {\r\n      this.deptOrgVisible = true;\r\n    },\r\n    // 关闭审查机构弹窗\r\n    deptOrgCancel() {\r\n      this.deptOrgVisible = false;\r\n    },\r\n    // 点击打开送审人弹窗\r\n    choiceEntrustedUnitClick() {\r\n      this.entrustedUnitOrgVisible = true;\r\n    },\r\n    // 点击关闭送审人弹窗\r\n    entrustedUnitOrgCancel() {\r\n      this.entrustedUnitOrgVisible = false;\r\n    },\r\n    entrustedUnitSure() {\r\n      const res = this.zxcheckedData[0];\r\n      console.log(res, 'res');\r\n      this.tableQuery.submitter = res.name;\r\n      this.entrustedUnitOrgVisible = false;\r\n    },\r\n    choiceNoticeDeptSure() {\r\n      let c = '';\r\n      let cid = '';\r\n      this.zxcheckedData.forEach((item) => {\r\n        if (c.length === 0) {\r\n          c = c + item.name;\r\n          cid = cid + item.unitId;\r\n        } else {\r\n          c = c + ',' + item.name;\r\n          cid = cid + ',' + item.unitId;\r\n        }\r\n      });\r\n      this.tableQuery.createOgnName = c;\r\n      this.deptOrgVisible = false;\r\n    },\r\n    add_(event) {\r\n      let tableName;\r\n      const name = this.utils.getDicName(this.utils.compliance_review_type, event);\r\n      const tabId = this.utils.createUUID();\r\n      console.log('name:', name);\r\n      console.log('name.id:', name?.id);\r\n      console.log('event:', event);\r\n      if (name ==='重大决策事项合规审查'){\r\n         tableName='hgsc_main_detail';\r\n      }else if (name ==='重要请示事项合规审查'){\r\n         tableName='hgsc_main_detail_qsss';\r\n      }\r\n      else if (name ==='内部规章制度合法合规审查'){\r\n         tableName='hgsc_main_detail_zdhf';\r\n      }\r\n      else if (name ==='特殊经营类合规论证'){\r\n         tableName='hgsc_main_detail_tsjy';\r\n      }\r\n      else if (name ==='合法合规审查意见'){\r\n         tableName='hgsc_main_detail_hfhg';\r\n      }\r\n\r\n      this.layout.openNewTab(name, tableName, tableName, tabId, {\r\n        functionId: tableName +',' + tabId,\r\n        ...this.utils.routeState.NEW(tabId),\r\n        reviewCategory: event, reviewCategoryName: name,\r\n      });\r\n    },\r\n    // 编辑\r\n    edit_(index, row) {\r\n      let tableName;\r\n      if (row.reviewCategoryName ==='重大决策事项合规审查'){\r\n        tableName='hgsc_main_detail';\r\n      }else if (row.reviewCategoryName ==='重要请示事项合规审查'){\r\n        tableName='hgsc_main_detail_qsss';\r\n      }\r\n      else if (row.reviewCategoryName ==='内部规章制度合法合规审查'){\r\n        tableName='hgsc_main_detail_zdhf';\r\n      }\r\n      else if (row.reviewCategoryName ==='特殊经营类合规论证'){\r\n        tableName='hgsc_main_detail_tsjy';\r\n      }\r\n      else if (row.reviewCategoryName ==='合法合规审查意见'){\r\n        tableName='hgsc_main_detail_hfhg';\r\n      }\r\n      console.log(row, 'row');\r\n      const tabId = this.utils.createUUID();\r\n      this.layout.openNewTab(row.reviewCategoryName, tableName, tableName, row.id, {\r\n        functionId: tableName+','  + row.id,\r\n        ...this.utils.routeState.EDIT(row.id),\r\n        view: 'old',\r\n        reviewCategory: row.reviewCategory, reviewCategoryName: row.reviewCategoryName\r\n      });\r\n    },\r\n    // 删除\r\n    delete_(index, row) {\r\n      this.$confirm('此操作将永久删除该数据及相关数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      })\r\n        .then(() => {\r\n          new Promise((resolve, reject) => {\r\n            complianceReviewApi\r\n              .deletebyid({\r\n                id: row.id,\r\n              })\r\n              .then((response) => {\r\n                resolve(response);\r\n              });\r\n          }).then((value) => {\r\n            this.tableData.splice(index, 1);\r\n            this.$message.success('删除成功!');\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除',\r\n          });\r\n        });\r\n    },\r\n    // 查看\r\n    view_(index, row) {\r\n      let tableName;\r\n      if (row.reviewCategoryName ==='重大决策事项合规审查'){\r\n        tableName='hgsc_main_detail';\r\n      }else if (row.reviewCategoryName ==='重要请示事项合规审查'){\r\n        tableName='hgsc_main_detail_qsss';\r\n      }\r\n      else if (row.reviewCategoryName ==='内部规章制度合法合规审查'){\r\n        tableName='hgsc_main_detail_zdhf';\r\n      }\r\n      else if (row.reviewCategoryName ==='特殊经营类合规论证'){\r\n        tableName='hgsc_main_detail_tsjy';\r\n      }\r\n      else if (row.reviewCategoryName ==='合法合规审查意见'){\r\n        tableName='hgsc_main_detail_hfhg';\r\n      }\r\n      if (row.dataStateCode == this.utils.dataState_BPM.SAVE.code) {\r\n        const tabId = this.utils.createUUID();\r\n        this.layout.openNewTab('合同合规审查', tableName, tableName, tabId, {\r\n          functionId: tableName+',' + tabId,\r\n          ...this.utils.routeState.VIEW(row.id),\r\n          reviewCategoryName: row.reviewCategoryName\r\n        });\r\n      } else {\r\n        taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then((res) => {\r\n          const functionId = res.data.data[0].ID;\r\n          const tabId = this.utils.createUUID();\r\n          this.layout.openNewTab('合规审查信息', 'design_page', 'design_page', tabId, {\r\n            processInstanceId: res.data.data[0].PID, //流程实例\r\n            taskId: res.data.data[0].ID, //任务ID\r\n            businessKey: row.id, //业务数据ID\r\n            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了\r\n            entranceType: 'FLOWABLE',\r\n            type: 'haveDealt',\r\n            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮\r\n            view: 'new',\r\n            reviewCategoryName: row.reviewCategoryName\r\n          });\r\n        });\r\n      }\r\n    },\r\n    rowDblclick(row, column, event) {\r\n      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code) {\r\n        const tabId = this.utils.createUUID();\r\n        this.layout.openNewTab('合规报告审批信息', tableName, tableName, tabId, {\r\n          functionId: tableName+',' + tabId,\r\n          ...this.utils.routeState.VIEW(row.id),\r\n        });\r\n      } else {\r\n        taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then((res) => {\r\n          const functionId = res.data.data[0].ID;\r\n          const tabId = this.utils.createUUID();\r\n          this.layout.openNewTab('合规报告审批信息', 'design_page', 'design_page', tabId, {\r\n            processInstanceId: res.data.data[0].PID, //流程实例\r\n            taskId: res.data.data[0].ID, //任务ID\r\n            businessKey: row.id, //业务数据ID\r\n            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了\r\n            entranceType: 'FLOWABLE',\r\n            type: 'haveDealt',\r\n            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮\r\n            view: 'new',\r\n          });\r\n        });\r\n      }\r\n    },\r\n    tableSort(column, prop, order) {\r\n      this.tableQuery.sortName = column.prop;\r\n      this.tableQuery.order = column.order === 'ascending';\r\n      this.refreshData();\r\n    },\r\n    // 点击搜索按钮事件,回到第一页,重新刷新数据\r\n    search_: function () {\r\n      this.tableQuery.page = 1;\r\n      this.refreshData();\r\n    },\r\n    // 点击清空按钮事件,清空查询条件属性,回到第一页,重新刷新数据\r\n    empty_() {\r\n      // 清空搜索条件\r\n      this.tableQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        total: 0,\r\n        riskName: '',\r\n        caseCode: '',\r\n        expectedRiskLevel: '',\r\n        fuzzyValue: '',\r\n        isQuery: true,\r\n      };\r\n      this.refreshData();\r\n    },\r\n    // 点击刷新按钮事件\r\n    refresh_() {\r\n      this.tableQuery.sortName = null;\r\n      this.tableQuery.order = null;\r\n      this.empty_();\r\n    },\r\n    cancel() {\r\n      this.userDialogVisible = false;\r\n    },\r\n    choiceDeptSure() {\r\n      let selectedUnits = this.zxcheckedData.map((item) => item.name).join(', ');\r\n      this.tableQuery.reportingUnit = selectedUnits;\r\n      this.userDialogVisible = false;\r\n    },\r\n        exportExcel() {\r\n      this.exportLoading = true;\r\n\r\n      // 显示提示消息\r\n      const loadingMessage = this.$message({\r\n        message: '正在准备导出数据，请稍候...',\r\n        type: 'info',\r\n        duration: 0\r\n      });\r\n\r\n      const queryParams = {};\r\n      Object.assign(queryParams, this.tableQuery);\r\n      queryParams.limit = 9999;\r\n\r\n      let date = new Date();\r\n      let formatDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();\r\n\r\n      complianceReviewApi.exportRiskLedger(queryParams).then((response) => {\r\n        const blob = response.data;\r\n        const fileName = formatDate + '合规审查台账.xlsx';\r\n\r\n        // 关闭提示消息\r\n        loadingMessage.close();\r\n\r\n        // 下载文件\r\n        if ('download' in document.createElement('a')) {\r\n          const elink = document.createElement('a');\r\n          elink.download = fileName;\r\n          elink.style.display = 'none';\r\n          elink.href = URL.createObjectURL(blob);\r\n          document.body.appendChild(elink);\r\n          elink.click();\r\n          URL.revokeObjectURL(elink.href);\r\n          document.body.removeChild(elink);\r\n\r\n          this.$message.success('导出成功');\r\n        } else {\r\n          navigator.msSaveBlob(blob, fileName);\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('导出失败：' + (error.message || '未知错误'));\r\n      }).finally(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.el-table__fixed-body-wrapper {\r\n  top: 50px !important;\r\n}\r\n</style>\r\n"]}]}