{"remainingRequest": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Desktop\\XM\\FW\\baogang\\browser\\src\\view\\Compliance\\Management\\ComplianceOperation\\ComplianceReport\\HgscTz.vue?vue&type=template&id=01812689&scoped=true&", "dependencies": [{"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\src\\view\\Compliance\\Management\\ComplianceOperation\\ComplianceReport\\HgscTz.vue", "mtime": 1756905062235}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<el-container class=\"container-manage-sg\" direction=\"vertical\">\n  <el-header>\n    <el-card>\n      <div>\n        <el-input v-model=\"tableQuery.fuzzyValue\" class=\"filter_input\" clearable placeholder=\"检索字段（事项题目、审查分类、送审部门）\"\n          @clear=\"refreshData\" @keyup.enter.native=\"refreshData\">\n          <el-popover slot=\"prepend\" placement=\"bottom-start\" trigger=\"click\" width=\"1000\">\n            <el-form ref=\"queryForm\" label-width=\"100px\" size=\"mini\">\n              <!-- 选择部门弹窗 -->\n              <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"deptOrgVisible\" title=\"选择部门\" width=\"50%\">\n                <div class=\"el-dialog-div\" style=\"z-index: 999999999\">\n                  <orgTree :accordion=\"false\" :checked-data.sync=\"zxcheckedData\" :is-check=\"true\"\n                    :is-checked-user=\"false\" :is-filter=\"true\" :is-not-cascade=\"true\" :show-user=\"false\" />\n                </div>\n                <span slot=\"footer\" class=\"dialog-footer\">\n                  <el-button class=\"negative-btn\" icon=\"\" @click=\"deptOrgCancel\">取消</el-button>\n                  <el-button class=\"active-btn\" icon=\"\" type=\"primary\" @click=\"choiceNoticeDeptSure\">确定</el-button>\n                </span>\n              </el-dialog>\n              <!-- 选择送审人弹窗 -->\n              <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"entrustedUnitOrgVisible\" title=\"选择送审人\"\n                width=\"50%\">\n                <div class=\"el-dialog-div\">\n                  <orgTree :accordion=\"false\" :checked-data.sync=\"zxcheckedData\" :is-check=\"false\"\n                    :is-checked-user=\"false\" :is-filter=\"true\" :is-not-cascade=\"true\" :show-user=\"true\" />\n                </div>\n                <span slot=\"footer\" class=\"dialog-footer\">\n                  <el-button class=\"negative-btn\" icon=\"\" @click=\"entrustedUnitOrgCancel\">取消</el-button>\n                  <el-button class=\"active-btn\" icon=\"\" type=\"primary\" @click=\"entrustedUnitSure\">确定</el-button>\n                </span>\n              </el-dialog>\n              <el-row>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"送审主题\">\n                    <el-input v-model=\"tableQuery.reviewSubject\" clearable placeholder=\"请输入...\" />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"送审分类\">\n                    <el-select v-model=\"tableQuery.reviewCategory\" clearable placeholder=\"请选择\" style=\"width: 100%\">\n                      <el-option v-for=\"item in utils.compliance_report_type\" :key=\"item.dicName\"\n                        :label=\"item.dicName\" :value=\"item.dicName\" />\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n                <el-row>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"审查状态\">\n                    <el-input v-model=\"tableQuery.dataState\" clearable placeholder=\"请输入...\" />\n                  </el-form-item>\n                </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"业务领域\" >\n                  <el-select v-model=\"tableQuery.businessArea\" clearable placeholder=\"请选择\" style=\"width: 100%\">\n                    <el-option v-for=\"item in businessAreaData\" :key=\"item.dicName\" :label=\"item.dicName\"\n                      :value=\"item.dicName\" />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"12\">\n                  <!-- <el-form-item label=\"送审部门\">\n                    <el-input v-model=\"tableQuery.createOgnName\" placeholder=\"请选择\" class=\"input-with-select\">\n                      <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"showOrgTreeDialog\"/>\n                    </el-input>\n                  </el-form-item> -->\n                  <el-form-item label=\"送审部门\" prop=\"createOgnName\">\n                    <el-input v-model=\"tableQuery.createOgnName\" class=\"input-with-select\" clearable\n                      placeholder=\"请选择\">\n                      <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"chooseNoticeDeptClick\" />\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"送审人\">\n                    <el-input v-model=\"tableQuery.submitter\" class=\"input-with-select\" clearable placeholder=\"请选择\">\n                      <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"choiceEntrustedUnitClick\" />\n                    </el-input>\n                    <!-- <el-input v-model=\"tableQuery.submitter\" placeholder=\"请选择\" class=\"input-with-select\">\n                      <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"showUserTreeDialog\"/>\n                    </el-input> -->\n                    <!-- <span v-else class=\"viewSpan\">{{ mainData.riskDepartment }}</span> -->\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"送审时间\">\n                    <el-date-picker v-model=\"tableQuery.submissionDateStart\" clearable placeholder=\"选择日期\"\n                      style=\"width: 45%; float: left\" type=\"date\" />\n                    <div class=\"label_1\" style=\"width: 10%; float: left; text-align: center\"><span>至</span></div>\n                    <el-date-picker v-model=\"tableQuery.submissionDateEnd\" clearable placeholder=\"选择日期\"\n                      style=\"width: 45%\" type=\"date\" />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-button-group style=\"float: right\">\n                <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"search_\">搜索</el-button>\n                <el-button icon=\"el-icon-refresh-left\" size=\"mini\" type=\"primary\" @click=\"empty_\">重置</el-button>\n              </el-button-group>\n            </el-form>\n            <!-- el-dialog 组件 -->\n            <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"userDialogVisible\" title=\"选择送审人\" width=\"50%\">\n              <div class=\"el-dialog-div\">\n                <orgTree :accordion=\"false\" :checked-data.sync=\"zxcheckedData\" :is-check=\"true\"\n                  :is-checked-user=\"isCheckedUser\" :is-filter=\"true\" :is-not-cascade=\"true\" :show-user=\"showUser\" />\n              </div>\n              <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button class=\"negative-btn\" icon=\"\" @click=\"cancel\">取消</el-button>\n                <el-button class=\"active-btn\" icon=\"\" type=\"primary\" @click=\"choiceDeptSure\">确定</el-button>\n              </span>\n            </el-dialog>\n            <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"orgDialogVisible\" title=\"选择送审部门\" width=\"50%\">\n              <div class=\"el-dialog-div\">\n                <orgTree :accordion=\"false\" :checked-data.sync=\"zxcheckedData\" :is-check=\"true\"\n                  :is-checked-user=\"false\" :is-filter=\"true\" :is-not-cascade=\"true\" :show-user=\"false\" />\n              </div>\n              <span slot=\"footer\" class=\"dialog-footer\">\n                <el-button class=\"negative-btn\" icon=\"\" @click=\"cancel\">取消</el-button>\n                <el-button class=\"active-btn\" icon=\"\" type=\"primary\" @click=\"choiceDeptSure\">确定</el-button>\n              </span>\n            </el-dialog>\n            <el-button slot=\"reference\" size=\"small\" type=\"primary\">高级检索</el-button>\n          </el-popover>\n          <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"search_\" />\n        </el-input>\n      </div>\n    </el-card>\n  </el-header>\n\n  <el-main>\n    <!-- 选择送审单位 -->\n    <SimpleBoardIndex :title=\"'合规审查台账'\">\n      <template slot=\"button\">\n        <el-button class=\"normal-btn\" size=\"mini\" type=\"primary\" @click=\"exportExcel\">导出Excel</el-button>\n      </template>\n      <el-table ref=\"table\" v-loading=\"tableLoading\" :data=\"tableData\" :height=\"table_height\"\n        :show-overflow-tooltip=\"true\" border fit highlight-current-row row-key=\"id\" size=\"mini\" stripe\n        style=\"table-layout: fixed; width: 100%\" @sort-change=\"tableSort\" @row-dblclick=\"rowDblclick\">\n        <el-table-column align=\"center\" label=\"序号\" type=\"index\" width=\"50\" />\n        <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'reviewSubject').visible\" label=\"事项题目\"\n          min-width=\"250\" prop=\"reviewSubject\" show-overflow-tooltip />\n        <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'reviewCategoryName').visible\" label=\"审查分类\"\n          min-width=\"180\" prop=\"reviewCategoryName\" show-overflow-tooltip sortable=\"custom\" />\n          <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'businessArea').visible\" label=\"业务领域\"\n          min-width=\"120\" prop=\"businessArea\" show-overflow-tooltip />\n        <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'submitter').visible\" label=\"送审人\"\n          min-width=\"100\" prop=\"submitter\" show-overflow-tooltip />\n        <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'createOgnName').visible\" label=\"送审单位\"\n          min-width=\"250\" prop=\"createOgnName\" show-overflow-tooltip />\n        <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'submissionDate').visible\" label=\"送审时间\"\n          min-width=\"100\" prop=\"submissionDate\" show-overflow-tooltip sortable=\"custom\">\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.submissionDate | parseTime('{y}-{m}-{d}') }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"审查状态\" min-width=\"100\" prop=\"dataState\" show-overflow-tooltip sortable=\"custom\" />\n        <el-table-column align=\"center\" fixed=\"right\" label=\"操作\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <!-- <el-button v-if=\"scope.row.dataState == '已保存'\" type=\"text\" @click=\"edit_(scope.$index, scope.row)\">\n              编辑\n            </el-button> -->\n            <el-button type=\"text\" @click=\"view_(scope.$index, scope.row)\">查看</el-button>\n            <!-- <el-button v-if=\"scope.row.dataState == '已保存'\" type=\"text\" @click=\"delete_(scope.$index, scope.row)\">\n              删除\n            </el-button> -->\n          </template>\n        </el-table-column>\n      </el-table>\n    </SimpleBoardIndex>\n  </el-main>\n  <el-footer>\n    <!--分页工具栏-->\n    <pagination :limit.sync=\"tableQuery.limit\" :page.sync=\"tableQuery.page\" :total=\"tableQuery.total\"\n      @pagination=\"refreshData\" />\n  </el-footer>\n</el-container>\n", null]}