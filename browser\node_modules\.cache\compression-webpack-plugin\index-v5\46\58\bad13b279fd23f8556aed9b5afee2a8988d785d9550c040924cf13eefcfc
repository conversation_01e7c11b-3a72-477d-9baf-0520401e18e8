
38fa42d46ef79c9b384fc5b9095896c149b873c9	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fapp.1756902943813.js\",\"contentHash\":\"c76be8774a547ef5aff9d9c723333715\"}","integrity":"sha512-VZ8uFOGZXjt5aoTYsVVwsjI6WOfoCdtZk6EUmApoXXNgxG/86Esznz8clkrK9Bwws0fK0pFjTUeovKH0VD/q+Q==","time":1756905090174,"size":60603600}