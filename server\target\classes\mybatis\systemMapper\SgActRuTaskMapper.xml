<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.systemDao.SgActRuTaskMapper">

    <resultMap id="BaseResultMap" type="com.klaw.entity.systemBean.SgActRuTask">
        <id property="id" column="ID_" jdbcType="VARCHAR"/>
        <result property="rev" column="REV_" jdbcType="INTEGER"/>
        <result property="executionId" column="EXECUTION_ID_" jdbcType="VARCHAR"/>
        <result property="procInstId" column="PROC_INST_ID_" jdbcType="VARCHAR"/>
        <result property="procDefId" column="PROC_DEF_ID_" jdbcType="VARCHAR"/>
        <result property="taskDefId" column="TASK_DEF_ID_" jdbcType="VARCHAR"/>
        <result property="scopeId" column="SCOPE_ID_" jdbcType="VARCHAR"/>
        <result property="subScopeId" column="SUB_SCOPE_ID_" jdbcType="VARCHAR"/>
        <result property="scopeType" column="SCOPE_TYPE_" jdbcType="VARCHAR"/>
        <result property="scopeDefinitionId" column="SCOPE_DEFINITION_ID_" jdbcType="VARCHAR"/>
        <result property="propagatedStageInstId" column="PROPAGATED_STAGE_INST_ID_" jdbcType="VARCHAR"/>
        <result property="name" column="NAME_" jdbcType="VARCHAR"/>
        <result property="parentTaskId" column="PARENT_TASK_ID_" jdbcType="VARCHAR"/>
        <result property="description" column="DESCRIPTION_" jdbcType="VARCHAR"/>
        <result property="taskDefKey" column="TASK_DEF_KEY_" jdbcType="VARCHAR"/>
        <result property="owner" column="OWNER_" jdbcType="VARCHAR"/>
        <result property="assignee" column="ASSIGNEE_" jdbcType="VARCHAR"/>
        <result property="delegation" column="DELEGATION_" jdbcType="VARCHAR"/>
        <result property="priority" column="PRIORITY_" jdbcType="INTEGER"/>
        <result property="createTime" column="CREATE_TIME_" jdbcType="TIMESTAMP"/>
        <result property="dueDate" column="DUE_DATE_" jdbcType="TIMESTAMP"/>
        <result property="category" column="CATEGORY_" jdbcType="VARCHAR"/>
        <result property="suspensionState" column="SUSPENSION_STATE_" jdbcType="INTEGER"/>
        <result property="tenantId" column="TENANT_ID_" jdbcType="VARCHAR"/>
        <result property="formKey" column="FORM_KEY_" jdbcType="VARCHAR"/>
        <result property="claimTime" column="CLAIM_TIME_" jdbcType="TIMESTAMP"/>
        <result property="isCountEnabled" column="IS_COUNT_ENABLED_" jdbcType="TINYINT"/>
        <result property="varCount" column="VAR_COUNT_" jdbcType="INTEGER"/>
        <result property="idLinkCount" column="ID_LINK_COUNT_" jdbcType="INTEGER"/>
        <result property="subTaskCount" column="SUB_TASK_COUNT_" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID_
        ,REV_,EXECUTION_ID_,
        PROC_INST_ID_,PROC_DEF_ID_,TASK_DEF_ID_,
        SCOPE_ID_,SUB_SCOPE_ID_,SCOPE_TYPE_,
        SCOPE_DEFINITION_ID_,PROPAGATED_STAGE_INST_ID_,NAME_,
        PARENT_TASK_ID_,DESCRIPTION_,TASK_DEF_KEY_,
        OWNER_,ASSIGNEE_,DELEGATION_,
        PRIORITY_,CREATE_TIME_,DUE_DATE_,
        CATEGORY_,SUSPENSION_STATE_,TENANT_ID_,
        FORM_KEY_,CLAIM_TIME_,IS_COUNT_ENABLED_,
        VAR_COUNT_,ID_LINK_COUNT_,SUB_TASK_COUNT_
    </sql>

    <select id="selectTask" resultType="java.util.Map">
        SELECT *
        FROM (
                 SELECT T1.ID_,
                        T1.NAME_,
                        T1.FORM_KEY_,
                        T1.ASSIGNEE_,
                        T1.EXECUTION_ID_,
                        T1.TASK_DEF_KEY_,
                        T3.TENANT_ID_,
                        T3.BUSINESS_KEY_,
                        T3.PROC_INST_ID_,
                        T1.CREATE_TIME_,
                        T1.PROC_DEF_ID_,
                        P.NAME_ AS PROCESS_DEFINITION_NAME,
                        T1.PRIORITY_
                 FROM ACT_RU_TASK T1
                          INNER JOIN ACT_RU_EXECUTION T3 ON T1.PROC_INST_ID_ = T3.ID_
                          INNER JOIN ACT_RE_PROCDEF P ON P.ID_ = T1.PROC_DEF_ID_
                 WHERE T1.ASSIGNEE_ = #{userID}
                 UNION
                 SELECT T1.ID_,
                        T1.NAME_,
                        T1.FORM_KEY_,
                        T1.ASSIGNEE_,
                        T1.EXECUTION_ID_,
                        T1.TASK_DEF_KEY_,
                        T3.TENANT_ID_,
                        T3.BUSINESS_KEY_,
                        T3.PROC_INST_ID_,
                        T1.CREATE_TIME_,
                        T1.PROC_DEF_ID_,
                        P.NAME_ AS PROCESS_DEFINITION_NAME,
                        T1.PRIORITY_
                 FROM ACT_RU_TASK T1
                          INNER JOIN ACT_RU_EXECUTION T3 ON T1.PROC_INST_ID_ = T3.ID_
                          INNER JOIN ACT_RE_PROCDEF P ON P.ID_ = T1.PROC_DEF_ID_
                          LEFT JOIN ACT_RU_IDENTITYLINK I ON I.TASK_ID_ = T1.ID_
                 WHERE I.USER_ID_ = #{userID}
             ) T1
        ORDER BY T1.PRIORITY_ DESC,
                 T1.CREATE_TIME_ DESC
    </select>

    <select id="selectHistoryTask" resultType="java.util.Map">
        SELECT T1.ID_,
               T1.NAME_,
               T1.FORM_KEY_,
               T1.ASSIGNEE_,
               T1.TASK_DEF_KEY_,
               T1.EXECUTION_ID_,
               T3.BUSINESS_KEY_,
               T3.PROC_INST_ID_,
               T3.TENANT_ID_,
               T1.START_TIME_,
               T1.END_TIME_,
               T1.PROC_DEF_ID_,
               P.NAME_ AS PROCESS_DEFINITION_NAME,
               T1.PRIORITY_
        FROM ACT_HI_TASKINST T1
                 LEFT JOIN ACT_HI_PROCINST T3 ON T1.PROC_INST_ID_ = T3.PROC_INST_ID_
                 INNER JOIN ACT_RE_PROCDEF P ON P.ID_ = T1.PROC_DEF_ID_
        WHERE t1.END_TIME_ IS NOT NULL
          AND T1.ASSIGNEE_ = #{userID}
        ORDER BY T1.PRIORITY_ DESC,
                 T1.END_TIME_ DESC
    </select>

<select id="selectTaskId" resultType="java.util.Map">
    SELECT T1.ID_ as taskId,
           T1.PROC_INST_ID_ as processInstanceId,
           A.ACT_NAME_ as name,
           A.ACT_ID_ as nameId,
           T1.TASK_DEF_KEY_ as nodeId,
           T1.ASSIGNEE_ as assignee,
           T1.START_TIME_ as startTime,
           T1.END_TIME_ as endTime,
           T1.PROC_DEF_ID_ as processDefinitionId,
           T1.PROC_DEF_ID_ as processDefinitionName
    FROM ACT_HI_TASKINST T1
    LEFT JOIN ACT_HI_ACTINST A ON A.PROC_INST_ID_ = T1.PROC_INST_ID_
                               AND A.ACT_TYPE_ = 'userTask'
                               AND A.END_TIME_ IS NULL
    WHERE
        T1.PROC_INST_ID_ = (SELECT PROC_INST_ID_ FROM ACT_HI_PROCINST WHERE BUSINESS_KEY_ = #{businessKey} order by start_time_ desc limit 1)
        <if test="isView != null and isView != ''">
            and T1.end_time_ is not null
        </if>
    order by T1.START_TIME_ desc
</select>



    <select id="selectAllProcessId" resultType="java.util.Map">
        SELECT
        max( ID_ ) AS ID,
        PROC_INST_ID_ AS PID
        FROM
        ( SELECT * FROM ACT_HI_TASKINST WHERE PROC_INST_ID_ IN (
            SELECT PROC_INST_ID_ FROM ACT_HI_PROCINST WHERE BUSINESS_KEY_ =  #{businessKey} ORDER BY start_time_ DESC
        ) ORDER BY start_time_ DESC ) t
        GROUP BY
        PROC_INST_ID_
        ORDER BY
        2 DESC
    </select>

    <select id="selectFunctionId" resultType="java.util.Map">
        SELECT FUNCTION_ID as ID,
               FUNCTION_CODE as code
        FROM SYS_FUNCTION_B
        WHERE
            FUNCTION_CODE = #{functionCode}
    </select>
</mapper>
