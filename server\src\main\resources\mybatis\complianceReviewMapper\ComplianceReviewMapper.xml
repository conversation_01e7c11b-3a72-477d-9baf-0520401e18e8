<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.complianceReviewDao.ComplianceReviewMapper">

    <select id="queryPageList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from compliance_review
        <where>
            ${ew.sqlSegment}
        </where>
    </select>


</mapper>
