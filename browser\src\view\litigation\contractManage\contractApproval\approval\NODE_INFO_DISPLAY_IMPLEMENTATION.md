# 岗位职责显示功能实现

## 🎯 功能概述

在新增合同审批单页面上方添加了岗位职责显示行，通过调用queryById接口获取当前节点信息，并根据nodeId映射显示对应的岗位名称。

## ✨ 实现内容

### 1. 界面显示组件

#### 🎨 岗位职责显示行
```vue
<!-- 岗位职责显示行 -->
<el-row v-if="currentNodeInfo" style="margin-top: 10px;">
  <el-col :span="24" style="text-align: center;">
    <el-tag type="info" size="medium" style="padding: 8px 16px; font-size: 14px;">
      <i class="el-icon-user-solid" style="margin-right: 5px;"></i>
      当前岗位职责：{{ currentNodeInfo }}
    </el-tag>
  </el-col>
</el-row>
```

#### 📍 显示位置
- 位于"新增合同审批单"标题上方
- 居中显示，使用蓝色信息标签
- 带有用户图标，增强视觉效果

### 2. 数据获取逻辑

#### 📡 接口调用方法
```javascript
// 获取岗位职责信息
getCurrentNodeInfo() {
  // 如果有ID参数，说明是查看已有数据，需要调用queryById接口
  if (this.$route.query.id) {
    contractApi.queryById(this.$route.query.id).then(res => {
      if (res && res.data && res.data.currentNodes && res.data.currentNodes.length > 0) {
        const nodeId = res.data.currentNodes[0].nodeId;
        this.currentNodeInfo = this.getNodeNameByNodeId(nodeId);
      }
    }).catch(error => {
      console.error('获取当前节点信息失败:', error);
    });
  }
}
```

#### 🗺️ NodeId映射方法
```javascript
// 根据nodeId获取岗位名称
getNodeNameByNodeId(nodeId) {
  const nodeMap = {
    'Activity_1rmp80v': '业务部门负责人',
    'Activity_1yc9eu3': '运改部审核',
    'Activity_1upb5zy': '税务审核',
    'Activity_0hgi73c': '资金审核',
    'Activity_0no6qkt': '财务部部长',
    'Activity_1qs8r6i': '法务部风控',
    'Activity_1lee3nx': '办公室',
    'Activity_1umzmjb': '公司领导',
    'Activity_0wn3tir': '返回经办人',
    'Activity_0y3xjh6': '法务承办人',
    'Activity_1e2ebp6': '合同专业负责人',
    'Activity_0pdswu8': '法务部部长',
    'Activity_0pz4x4e': '首席合规官',
    'Activity_1r1du0j': '三级审批',
    'Activity_0u0241c': '三级审批'
  };
  
  return nodeMap[nodeId] || '未知岗位';
}
```

### 3. 数据结构处理

#### 📋 接口返回数据格式
```javascript
{
  "currentNodes": [
    {
      "processInstanceId": "3292654",
      "processDefinitionId": "BGJT_HTQC_01:13:3292653",
      "name": "三级审批",
      "startTime": "2025-08-25 21:53:42",
      "assignee": "60019370",
      "endTime": "2025-08-25 23:11:26",
      "nodeId": "Activity_1r1du0j",  // 关键字段
      "taskId": "3292741"
    }
  ]
}
```

#### 🔍 数据提取逻辑
```javascript
// 从接口返回数据中提取nodeId
const nodeId = res.data.currentNodes[0].nodeId;

// 通过nodeId映射获取岗位名称
this.currentNodeInfo = this.getNodeNameByNodeId(nodeId);
```

## 🔧 技术实现

### 1. 组件数据属性

#### 📊 新增数据属性
```javascript
data() {
  return {
    // ... 其他属性
    currentNodeInfo: null, // 当前岗位职责信息
  }
}
```

### 2. 生命周期集成

#### 🚀 mounted钩子
```javascript
mounted() {
  // 原有的回调函数设置
  this.$emit('setCallBack', {
    // ... 原有回调
  })
  
  // 🆕 获取当前岗位职责信息
  this.getCurrentNodeInfo()
}
```

### 3. 条件显示逻辑

#### 🎯 显示条件
- 只有当 `currentNodeInfo` 有值时才显示岗位职责行
- 使用 `v-if="currentNodeInfo"` 进行条件渲染
- 避免在没有数据时显示空白标签

## 📊 NodeId映射表

| NodeId | 岗位名称 | 说明 |
|--------|----------|------|
| Activity_1rmp80v | 业务部门负责人 | 业务部门审核节点 |
| Activity_1yc9eu3 | 运改部审核 | 运营改革部审核 |
| Activity_1upb5zy | 税务审核 | 税务部门审核 |
| Activity_0hgi73c | 资金审核 | 资金管理审核 |
| Activity_0no6qkt | 财务部部长 | 财务部门负责人 |
| Activity_1qs8r6i | 法务部风控 | 法务风险控制 |
| Activity_1lee3nx | 办公室 | 办公室审核 |
| Activity_1umzmjb | 公司领导 | 公司高层审批 |
| Activity_0wn3tir | 返回经办人 | 退回经办人处理 |
| Activity_0y3xjh6 | 法务承办人 | 法务具体承办 |
| Activity_1e2ebp6 | 合同专业负责人 | 合同专业审核 |
| Activity_0pdswu8 | 法务部部长 | 法务部门负责人 |
| Activity_0pz4x4e | 首席合规官 | 合规管理负责人 |
| Activity_1r1du0j | 三级审批 | 三级审批节点 |
| Activity_0u0241c | 三级审批 | 三级审批节点 |

## 🎨 样式设计

### 1. 标签样式
```css
/* 岗位职责标签样式 */
.el-tag {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 4px;
}

/* 图标样式 */
.el-icon-user-solid {
  margin-right: 5px;
  color: #409eff;
}
```

### 2. 布局样式
```css
/* 居中显示 */
.el-col {
  text-align: center;
}

/* 上边距 */
.el-row {
  margin-top: 10px;
}
```

## 🧪 测试验证

### 1. 测试页面功能
- **NodeId映射测试**: 验证所有nodeId都能正确映射到岗位名称
- **接口调用模拟**: 模拟queryById接口返回数据
- **界面显示测试**: 验证岗位职责标签的显示效果
- **条件渲染测试**: 验证有无数据时的显示状态

### 2. 测试用例
```javascript
// 测试数据
const testCases = [
  { nodeId: 'Activity_1r1du0j', expected: '三级审批' },
  { nodeId: 'Activity_1rmp80v', expected: '业务部门负责人' },
  { nodeId: 'Activity_unknown', expected: '未知岗位' }
];
```

## 🚀 部署说明

### 1. 使用条件
- 页面URL包含id参数时才会调用接口获取节点信息
- 适用于查看已有合同审批单的场景
- 新建合同时不会显示岗位职责信息

### 2. 错误处理
- 接口调用失败时在控制台输出错误信息
- 未知nodeId时显示"未知岗位"
- 没有currentNodes数据时不显示岗位职责行

### 3. 性能考虑
- 只在有id参数时才调用接口
- 使用简单的对象映射，查找效率高
- 条件渲染避免不必要的DOM元素

## 📋 功能特性

### ✅ 已实现功能
- [x] 岗位职责信息获取
- [x] NodeId到岗位名称的映射
- [x] 界面显示组件
- [x] 条件渲染逻辑
- [x] 错误处理机制
- [x] 测试验证页面

### 🎯 用户体验
- **直观显示**: 清晰显示当前处理岗位
- **视觉突出**: 使用标签和图标增强视觉效果
- **位置合理**: 位于标题上方，不影响主要内容
- **响应式**: 根据数据状态动态显示/隐藏

### 🔒 稳定性保证
- **异常处理**: 完善的错误捕获和处理
- **数据验证**: 检查接口返回数据的完整性
- **降级处理**: 未知nodeId时显示默认文本
- **条件渲染**: 避免显示空白或错误内容

## 🎉 总结

通过这次实现，新增合同审批单页面现在具备了：

1. **智能岗位识别**: 自动获取并显示当前处理岗位
2. **清晰的视觉提示**: 使用标签和图标突出显示
3. **完善的映射机制**: 支持所有已知的审批节点
4. **稳定的错误处理**: 确保各种异常情况下的正常运行
5. **良好的用户体验**: 直观地告知用户当前的审批环节

现在用户在查看合同审批单时，可以清楚地看到当前处于哪个审批环节，由哪个岗位负责处理，大大提升了流程的透明度和用户体验！
