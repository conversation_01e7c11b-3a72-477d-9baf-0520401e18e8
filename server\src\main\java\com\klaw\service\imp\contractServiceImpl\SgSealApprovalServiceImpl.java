package com.klaw.service.imp.contractServiceImpl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.config.HweiOBSConfig;
import com.klaw.constant.DataStateBPM;
import com.klaw.constant.contractListEnum.DataTypeEnum;
import com.klaw.dao.contractDao.SgSealApprovalMapper;
import com.klaw.entity.authorizationBean.Authorization;
import com.klaw.entity.contractBean.SgSealApproval;
import com.klaw.entity.contractBean.SgSealApprovalDetail;
import com.klaw.entity.contractBean.contract.BmContract;
import com.klaw.entity.contractBean.contract.BmContractSeal;
import com.klaw.entity.contractBean.contract.BmContractText;
import com.klaw.entity.oa.CooperateWithSeal;
import com.klaw.entity.systemBean.SgSysDoc;
import com.klaw.service.authorizationService.AuthorizationService;
import com.klaw.service.contractService.SgSealApprovalDetailService;
import com.klaw.service.contractService.SgSealApprovalService;
import com.klaw.service.contractService.contract.*;
import com.klaw.service.oaService.CooperateWithSealService;
import com.klaw.service.systemService.SgSysDocService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.utils.StringUtil;
import com.klaw.utils.Utils;
import com.klaw.utils.seal.WithSealUtils;
import com.klaw.vo.ApiJson;
import com.klaw.vo.contractVo.SgSealDetailList;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SgSealApprovalServiceImpl extends ServiceImpl<SgSealApprovalMapper, SgSealApproval> implements SgSealApprovalService {

    public final static String DYY_NAME = "待用印";
    public final static int DYY_CODE = 0;
    public final static String DSX_NAME = "待生效";
    public final static int DSX_CODE = 1;
    public final static String YSX_NAME = "已生效";
    public final static int YSX_CODE = 2;
    private static final Logger log = LoggerFactory.getLogger(SgSealApprovalServiceImpl.class);
    private final static String SEAL_LIST = "sealList";
    private final static String APPLE_APP_INDEX = "sealApp";
    private final static String APPLE_SUPPLE_APP_INDEX = "sealAppSupple";

    @Resource
    private SgSealApprovalMapper sgSealApprovalMapper;
    @Resource
    private SgSealApprovalDetailService sgSealApprovalDetailService;
    @Resource
    private BmContractService bmContractService;
    @Resource
    private BmContractTextService bmContractTextService;
    @Resource
    private AuthorizationService authorizationService;
    @Autowired
    private BmContractPerformService bmContractPerformService;
    @Autowired
    private BmContractToMainService bmContractToMainService;
    @Autowired
    private BmContractSealService bmContractSealService;
    @Autowired
    WithSealUtils withSealUtils;
    @Autowired
    private CooperateWithSealService cooperateWithSealService;
    @Autowired
    private HweiOBSConfig hweiOBSConfig;
    @Autowired
    private SgSysDocService sgSysDocService;

    /**
     * <AUTHOR>
     * @Description 反射循环赋值
     * @Date 2024/05/11 15:32
     * @Param [parent, subclass]
     **/
    public static Object parentSubclass(Object parent, Object subclass) {
        Field[] parents = parent.getClass().getDeclaredFields();//获取所有属性
        Field[] children = subclass.getClass().getSuperclass().getDeclaredFields();//获取父类所有属性
        try {
            for (Field fieldParent : parents) {
                fieldParent.setAccessible(true);
                String nameParent = fieldParent.getName(); //获取属性的名字
                Object valueParent = fieldParent.get(parent);//获取属性值
                for (Field fieldChild : children) {
                    fieldChild.setAccessible(true);
                    String nameChild = fieldChild.getName(); //获取属性的名字
                    Object valueChild = fieldChild.get(subclass);
                    if (nameChild.equals(nameParent)) {
                        fieldChild.set(subclass, valueParent);
                    }

                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return subclass;
    }

    @Override
    public Page<SgSealApproval> queryPageData(JSONObject json) {
        StopWatch sw = new StopWatch();
        sw.start();
        QueryWrapper<SgSealApproval> queryWrapper = new QueryWrapper<SgSealApproval>();
        getFilter(queryWrapper, json);
        PageUtils<SgSealApproval> page = new PageUtils<SgSealApproval>(json);
        List<SgSealApproval> sealApprovals = sgSealApprovalMapper.queryPageData(page, queryWrapper);
        sw.stop();
        System.out.println("用印台账查询耗时：");
        System.out.println(sw.getTotalTimeMillis());
        return page.setRecords(sealApprovals);
    }

    @Override
    @Transactional
    public boolean saveData(SgSealApproval sgSealApproval) {
        SgSealApproval old = getById(sgSealApproval.getId());
        if (!ObjectUtils.isEmpty(old) && old.getDataState().equals(DataStateBPM.FINISH.getValue()) && StringUtils.isNotBlank(old.getTakeEffectName()) && StringUtils.isBlank(sgSealApproval.getTakeEffectName())) {
            return false;
        }
        Utils.saveChilds(sgSealApproval.getDetailList(), "parent_id", sgSealApproval.getId(), sgSealApprovalDetailService);
        integrationChild(sgSealApproval);
        if ("待生效".equals(sgSealApproval.getTakeEffectName())) {
            final List<SgSealApprovalDetail> sgSealApprovalDetails = sgSealApprovalDetailService.list(new QueryWrapper<SgSealApprovalDetail>().eq("parent_id", sgSealApproval.getId()));
            boolean allMatch = sgSealApprovalDetails.stream().allMatch(obj -> obj.getDataState().equals("已用印"));
            if (allMatch) {
                if ("7".equals(sgSealApproval.getSealPurposeId())) {
                    sgSealApproval.setTakeEffectCode(4);
                    sgSealApproval.setTakeEffectName("已用印");
                } else {
                    sgSealApproval.setTakeEffectCode(2);
                    sgSealApproval.setTakeEffectName("待生效");
                    if ("合同用印".equals(sgSealApproval.getSealPurpose())) {
                        BmContract bmContract = bmContractService.getById(sgSealApproval.getContractId());
                        if (!sgSealApproval.getTakeEffectName().equals(bmContract.getTakeEffectName())) {
                            bmContract.setTakeEffectCode(sgSealApproval.getTakeEffectCode());
                            bmContract.setTakeEffectName(sgSealApproval.getTakeEffectName());
                            bmContractService.saveOrUpdate(bmContract);
                        }
                    } else {
                        sgSealApproval.setTakeEffectCode(2);
                        sgSealApproval.setTakeEffectName("待生效");
                    }
                }
            } else {
                if ("授权审批".equals(sgSealApproval.getSealPurpose())) {
                    sgSealApproval.setTakeEffectCode(0);
                    sgSealApproval.setTakeEffectName("待用印");
                } else {
                    sgSealApproval.setTakeEffectCode(null);
                    sgSealApproval.setTakeEffectName(null);
                }

            }
        }
        if ("合同用印".equals(sgSealApproval.getSealPurpose())) {
            if ("已生效".equals(sgSealApproval.getTakeEffectName())) {
                setBmContractAndSeal(sgSealApproval);
            }
        }
        // 授权审批生效之后将扫描件保存到授权并且授权状态改为正常
        if ("授权审批".equals(sgSealApproval.getSealPurpose()) && "已生效".equals(sgSealApproval.getTakeEffectName())) {
            Authorization authorization = authorizationService.queryDataById(sgSealApproval.getContractId());
            if (authorization != null) {
                authorization.setAuthStatus("正常");
                authorization.setAuthTakeEffectFile(sgSealApproval.getOriginalAttachment());
                authorizationService.updateById(authorization);
            }

        }

        return saveOrUpdate(sgSealApproval);
    }

    @Transactional
    public void setBmContractAndSeal(SgSealApproval sgSealApproval) {
        BmContract bmContract = bmContractService.getById(sgSealApproval.getContractId());
        bmContract.setArchivedStateCode(DataStateBPM.ARCHIVED_STATE_1.getKey());
        bmContract.setArchivedState(DataStateBPM.ARCHIVED_STATE_1.getValue());
        //判断生效时间为空时取 我方/对方盖章时间最晚的值
        if (sgSealApproval.getEffectiveTime() == null) {
            SgSealApprovalDetail sgSealApprovalDetail = sgSealApproval.getDetailList().stream().max(Comparator.comparing(SgSealApprovalDetail::getOurSealTime)).orElse(null);
            if (sgSealApprovalDetail != null && sgSealApprovalDetail.getOurSealTime() != null && sgSealApproval.getOtherSealTime() != null) {
                Date max = sgSealApprovalDetail.getOurSealTime().after(sgSealApproval.getOtherSealTime()) ? sgSealApprovalDetail.getOurSealTime() : sgSealApproval.getOtherSealTime();
                sgSealApproval.setEffectiveTime(max);
            }
        }
        bmContract.setContractTakeEffectDate(sgSealApproval.getEffectiveTime());
        bmContract.setTakeEffectCode(sgSealApproval.getTakeEffectCode());
        bmContract.setTakeEffectName(sgSealApproval.getTakeEffectName());
        bmContract.setContractTakeEffectFile(sgSealApproval.getOriginalAttachment());

        if (bmContract.getDataTypeCode() == DataTypeEnum.DATA_TYPE_2.getCode()) {
            bmContractService.updateNotAllById(bmContract.getAfterChangeMoney(), bmContract.getAfterChangeAmountRmb(), bmContract.getAfterChangeValueAddedTaxAmount(), "已变更", 5, "待更新", 3, bmContract.getOriginalContractId());
            //变更履行计划状态
            bmContractPerformService.changePerformStatus(bmContract.getOriginalContractId(), "3", "待更新");
            // todo wxc
            //向主数据推送原合同明细
            try {
                bmContractToMainService.sendToMainContractDetail(bmContract.getOriginalContractId());
            } catch (Exception e) {
                log.error("推送主数据失败", e);
            }
        }
        if (bmContract.getDataTypeCode() == DataTypeEnum.DATA_TYPE_3.getCode()) {
            QueryWrapper<BmContract> wrapper = new QueryWrapper<>();
            wrapper.eq("id", bmContract.getOriginalContractId()).or().eq("original_contract_id", bmContract.getOriginalContractId());
            List<BmContract> list = bmContractService.list(wrapper);
            for (BmContract contract : list) {
                contract.setChangeState("已终止");
                contract.setChangeStateCode(6);
            }
            bmContractService.saveOrUpdateBatch(list);
        }
        if (!Objects.equals(bmContract.getDataSourceCode(), "100008")) {
            bmContractService.sendSaveContractCode(bmContract);
        }
        try {
            bmContractToMainService.sendToMainContractDetail(bmContract.getId());
        } catch (Exception e) {
            log.error("推送主数据失败", e);
        }
        bmContractService.saveOrUpdate(bmContract);
    }

    public String getCode(String psnNames, String psnName) {
        if (psnNames == null) {
            psnNames = psnName;
        } else {
            if (!psnNames.contains(psnName)) {
                psnNames = psnNames + "," + psnName;
            }
        }
        return psnNames;
    }

    private void integrationChild(SgSealApproval sgSealApproval) {
        List<SgSealApprovalDetail> detailList = sgSealApproval.getDetailList();
        if (!CollectionUtils.isEmpty(detailList)) {
            String sealNames = "";
            String sealIds = "";
            String sealTypes = "";
            String sealTypeIds = "";
            String sealAdmins = "";
            String sealAdminIds = "";
            for (SgSealApprovalDetail sgSealApprovalDetail : detailList) {
                if ("".equals(sealNames)) {
                    sealNames = sgSealApprovalDetail.getSealName();
                    sealIds = sgSealApprovalDetail.getSealId();
                } else {
                    sealNames += "、" + sgSealApprovalDetail.getSealName();
                    sealIds += "," + sgSealApprovalDetail.getSealId();
                }
                if ("".equals(sealTypes)) {
                    sealTypes = sgSealApprovalDetail.getSealType();
                    sealTypeIds = sgSealApprovalDetail.getSealTypeId();
                } else {
                    sealTypes += "、" + sgSealApprovalDetail.getSealType();
                    sealTypeIds += "," + sgSealApprovalDetail.getSealTypeId();
                }
                if ("".equals(sealAdmins)) {
                    sealAdmins = sgSealApprovalDetail.getSealAdminOld();
                    sealAdminIds = sgSealApprovalDetail.getSealAdminIdOld();
                } else {
                    sealAdmins += "、" + sgSealApprovalDetail.getSealAdminOld();
                    sealAdminIds += "," + sgSealApprovalDetail.getSealAdminIdOld();
                }
            }
            sgSealApproval.setSealNames(sealNames);
            sgSealApproval.setSealIds(sealIds);
            sgSealApproval.setSealTypes(sealTypes);
            sgSealApproval.setSealTypeIds(sealTypeIds);
            sgSealApproval.setSealAdmins(sealAdmins);
            sgSealApproval.setSealAdminIds(sealAdminIds);
        } else {
            sgSealApproval.setSealNames(null);
            sgSealApproval.setSealIds(null);
            sgSealApproval.setSealTypes(null);
            sgSealApproval.setSealTypeIds(null);
            sgSealApproval.setSealAdmins(null);
            sgSealApproval.setSealAdminIds(null);
        }
    }

    @Override
    public SgSealApproval queryDataById(String id, String currentPsnId) {
        SgSealApproval sgSealApproval = getById(id);
        if (sgSealApproval.getSealPurpose().equals("合同用印")) {
            BmContract approval = bmContractService.getById(sgSealApproval.getContractId());
            if (approval != null && approval.getDataTypeCode() == 2) {
                sgSealApproval.setFile(approval.getContractFiles());
            }
        }
        List<SgSealApprovalDetail> list = sgSealApprovalDetailService.list(new QueryWrapper<SgSealApprovalDetail>().eq("parent_id", id));
        if (!CollectionUtils.isEmpty(list)) {
            sgSealApproval.setDetailList(list);
        }
        List<BmContractText> contractTextList = bmContractTextService.list(new QueryWrapper<BmContractText>().eq("parent_id", sgSealApproval.getContractId()).orderByAsc("create_time"));
        if (!CollectionUtils.isEmpty(contractTextList)) {
            final Map<String, List<BmContractText>> contractTextMap = contractTextList.stream().filter(c -> StringUtils.isNotEmpty(c.getStandardAttachmentId())).collect(Collectors.groupingBy(BmContractText::getStandardAttachmentId));
            for (int i = 0; i < contractTextList.size(); i++) {
                final List<BmContractText> sgContractTexts = contractTextMap.get(contractTextList.get(i).getId());
                if (!CollectionUtils.isEmpty(sgContractTexts)) {
                    contractTextList.removeAll(sgContractTexts);
                    contractTextList.addAll(sgContractTexts);
                }
            }
            sgSealApproval.setTextList(contractTextList);
        }
        return sgSealApproval;
    }

    @Override
    public SgSealApproval querysxDataById(String id) {
        SgSealApproval sgSealApproval = getById(id);
        List<SgSealApprovalDetail> list = sgSealApprovalDetailService.list(new QueryWrapper<SgSealApprovalDetail>().eq("parent_id", id));
        if (!CollectionUtils.isEmpty(list)) {
            sgSealApproval.setDetailList(list);
        }
        List<BmContractText> contractTextList = bmContractTextService.list(new QueryWrapper<BmContractText>().eq("parent_id", sgSealApproval.getContractId()).orderByAsc("create_time"));
        if (!CollectionUtils.isEmpty(contractTextList)) {
            final Map<String, List<BmContractText>> contractTextMap = contractTextList.stream().filter(c -> StringUtils.isNotEmpty(c.getStandardAttachmentId())).collect(Collectors.groupingBy(c -> c.getStandardAttachmentId()));
            for (int i = 0; i < contractTextList.size(); i++) {
                final List<BmContractText> sgContractTexts = contractTextMap.get(contractTextList.get(i).getId());
                if (!CollectionUtils.isEmpty(sgContractTexts)) {
                    contractTextList.removeAll(sgContractTexts);
                    contractTextList.addAll(sgContractTexts);
                }
            }
            sgSealApproval.setTextList(contractTextList);
        }
        return sgSealApproval;
    }

    @Override
    public boolean deleteDataById(String id) {
        sgSealApprovalDetailService.remove(new QueryWrapper<SgSealApprovalDetail>().eq("parent_id", id));
        return removeById(id);
    }

    @Override
    @Transactional
    public void saveDataFinish(String businessKey) {
        SgSealApproval sealApproval = getById(businessKey);
        sealApproval.setTakeEffectName("待用印");
        sealApproval.setTakeEffectCode(1);
        saveOrUpdate(sealApproval);
        List<SgSealApprovalDetail> detailList = sgSealApprovalDetailService.list(new QueryWrapper<SgSealApprovalDetail>().eq("parent_id", businessKey));
        if (!CollectionUtils.isEmpty(detailList)) {
            for (SgSealApprovalDetail sgSealApprovalDetail : detailList) {
                sgSealApprovalDetail.setDataState("待用印");
                sgSealApprovalDetail.setDataStateCode(1);
                sgSealApprovalDetailService.saveOrUpdate(sgSealApprovalDetail);
            }
        }
        if ("0".equals(sealApproval.getSealPurposeId())) {
            BmContract bmContract = bmContractService.getById(getById(businessKey).getContractId());
            if (!Objects.equals(bmContract.getDataSourceCode(), "100008")) {
                List<BmContractSeal> sgContractSealList = new ArrayList<>();
                for (SgSealApprovalDetail sgSealApprovalDetail : detailList) {
                    BmContractSeal bmContractSeal = new BmContractSeal();
                    bmContractSeal.setId(StringUtil.makeUUID());
                    bmContractSeal.setParentId(bmContract.getParentId());
                    bmContractSeal.setSealName(sgSealApprovalDetail.getSealName());
                    bmContractSeal.setSealId(sgSealApprovalDetail.getSealId());
                    bmContractSeal.setSealType(sgSealApprovalDetail.getSealType());
                    bmContractSeal.setSealTypeId(sgSealApprovalDetail.getSealTypeId());
                    bmContractSeal.setSealAdmin(sgSealApprovalDetail.getSealAdminOld());
                    bmContractSeal.setSealAdminId(sgSealApprovalDetail.getSealAdminIdOld());
                    bmContractSeal.setSealNumberOld(Integer.valueOf(sgSealApprovalDetail.getSealNumberOld()));
                    bmContractSeal.setPrintsNumberOld(Integer.valueOf(sgSealApprovalDetail.getPrintsNumberOld()));
                    bmContractSeal.setCreateTime(new Date());
                    bmContractSeal.setCreatePsnFullId(bmContract.getCreatePsnFullId());
                    bmContractSeal.setCreatePsnFullName(bmContract.getCreatePsnFullName());
                    sgContractSealList.add(bmContractSeal);
                }
                Utils.saveChilds(sgContractSealList, "parent_id", bmContract.getId(), bmContractSealService);
            }
        }
    }

    @Override
    public Page<SgSealDetailList> querySealDetailList(JSONObject json) {

        PageUtils<SgSealDetailList> pageUtils = new PageUtils<>(json);
        QueryWrapper<SgSealApproval> queryWrapper = new QueryWrapper<SgSealApproval>();
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String id = json.getString("id");
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";

        String contractCode = json.containsKey("contractCode") ? json.getString("contractCode") : "";

        if (StringUtils.isNotBlank(contractCode)) {
            queryWrapper.like("s.contract_code", contractCode);
        }
        String contractName = json.containsKey("contractName") ? json.getString("contractName") : "";
        if (StringUtils.isNotBlank(contractName)) {
            queryWrapper.like("s.contract_name", contractName);
        }
        String sealNames = json.containsKey("sealNames") ? json.getString("sealNames") : "";
        if (StringUtils.isNotBlank(sealNames)) {
            queryWrapper.like("s.seal_names", sealNames);
        }

        String createPsnName = json.containsKey("createPsnName") ? json.getString("createPsnName") : "";
        if (StringUtils.isNotBlank(createPsnName)) {
            queryWrapper.like("s.create_Psn_Name", createPsnName);
        }
        String createDeptName = json.containsKey("createDeptName") ? json.getString("createDeptName") : "";
        if (StringUtils.isNotBlank(createDeptName)) {
            queryWrapper.like("s.create_Dept_Name", createDeptName);
        }
        String isSeal = json.containsKey("isSeal") ? json.getString("isSeal") : "";
        if (StringUtils.isNotBlank(isSeal)) {
            if (isSeal.equals("是")) {
                queryWrapper.and(q -> q.apply("d.seal_number > d.seal_number_Old or d.prints_number > d.prints_Number_Old"));
            } else {
                queryWrapper.and(a -> a.and(q -> q.apply("d.seal_number <= d.seal_number_Old and d.prints_number <= d.prints_Number_Old")).or().apply("(seal_number IS NULL  OR seal_number_Old IS NULL OR prints_number IS NULL OR prints_Number_Old IS NULL)"));
            }
        }
        Date timeMin = json.containsKey("timeMin") ? json.getDate("timeMin") : null;
        Date timeMax = json.containsKey("timeMax") ? json.getDate("timeMax") : null;
        if (!ObjectUtils.isEmpty(timeMin)) {
            queryWrapper.ge("d.our_seal_time", timeMin);
        }
        if (!ObjectUtils.isEmpty(timeMax)) {
            queryWrapper.le("d.our_seal_time", timeMax);
        }

        //模糊搜索
        String fuzzyValue = json.getString("fuzzyValue");
        if (StringUtils.isNotBlank(id)) {
            queryWrapper.ne("id", id);
        }
        queryWrapper.isNotNull("s.contract_code");
        queryWrapper.and(q -> q.eq("s.take_Effect_Code", 1).or().eq("s.take_Effect_Code", 2).or().eq("s.take_Effect_Code", 3).or().eq("s.take_Effect_Code", 4));
        if (StringUtils.isNotBlank(fuzzyValue)) {
            queryWrapper.and(i -> i.like("s.contract_name", fuzzyValue).or().like("s.contract_code", fuzzyValue));
        }
        Long functionId = DataAuthUtils.getFunctionIdByCode("seal_ledger");
        if (isQuery) {
            DataAuthUtils.dataPermSql(queryWrapper, null, functionId, orgId);
        } else {
//            queryWrapper.like("s.create_psn_full_id", orgId);
            queryWrapper.and(i -> i.eq("s.create_org_id", orgId).or().like("s.seal_admin_ids", orgId));

        }

        return pageUtils.setRecords(sgSealApprovalMapper.querySealDetailList(pageUtils, queryWrapper));
    }

    @Override
    public Page<SgSealApproval> getSendElectronicSign(JSONObject json) {
        return this.queryPageData(json);
    }

    public void getFilter(QueryWrapper<SgSealApproval> queryWrapper, JSONObject json) {
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        String dataTypeCode = json.containsKey("dataTypeCode") ? json.getString("dataTypeCode") : "";
        String takeEffectCode = json.containsKey("takeEffectCode") ? json.getString("takeEffectCode") : "";
        String contractName = json.containsKey("contractName") ? json.getString("contractName") : "";
        String sealName = json.containsKey("sealName") ? json.getString("sealName") : "";
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : "";
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;
        Date effectiveTimeMin = json.containsKey("effectiveTimeMin") ? json.getDate("effectiveTimeMin") : null;
        Date effectiveTimeMax = json.containsKey("effectiveTimeMax") ? json.getDate("effectiveTimeMax") : null;
        String ourPartys = json.containsKey("ourPartys") ? json.getString("ourPartys") : null;
        String otherPartys = json.containsKey("otherPartys") ? json.getString("otherPartys") : null;
        String functionType = json.containsKey("functionType") ? json.getString("functionType") : null;

        if (StringUtils.isNotBlank(contractName)) {
            queryWrapper.like("m.contract_name", contractName);
        }
        if (StringUtils.isNotBlank(sealName)) {
            queryWrapper.like("m.seal_names", sealName);
        }
        if (StringUtils.isNotBlank(dataTypeCode)) {
            queryWrapper.like("data_type_code", dataTypeCode);
        }
        if (!ObjectUtils.isEmpty(effectiveTimeMin)) {
            queryWrapper.ge("m.effective_Time", effectiveTimeMin);
        }
        if (!ObjectUtils.isEmpty(effectiveTimeMax)) {
            queryWrapper.le("m.effective_Time", effectiveTimeMax);
        }
        if (!ObjectUtils.isEmpty(ourPartys)) {
            queryWrapper.like("d.our_party_name", ourPartys);
        }
        if (!ObjectUtils.isEmpty(otherPartys)) {
            queryWrapper.like("d.other_party_name", otherPartys);
        }
        if (StringUtils.isNotBlank(fuzzyValue)) {
            queryWrapper.and(i -> i.like("m.contract_name", fuzzyValue).or().like("d.our_party_name", fuzzyValue).or().like("d.other_party_name", fuzzyValue).or().like("d.approval_Code", fuzzyValue).or().like("m.contract_Code", fuzzyValue).or().like("m.seal_names", fuzzyValue));
        }
        if (StringUtils.isNotBlank(takeEffectCode)) {
            if (takeEffectCode.equals("1")) {
                queryWrapper.and(wrapper -> wrapper.eq("m.take_effect_code", takeEffectCode).or().eq("m.take_effect_code", 5).or().isNull("m.take_effect_code"));
            } else {
                queryWrapper.and(wrapper -> wrapper.eq("m.take_effect_code", takeEffectCode));
            }
        }

        if (SEAL_LIST.equals(functionType)) {
            if (takeEffectCode.equals("1")) {
                if (StringUtils.isNotBlank(orgId)) {
                    queryWrapper.and(i -> i.eq("m.create_org_id", orgId).or().like("m.seal_admin_ids", orgId));
                }
            } else {
                if (StringUtils.isNotBlank(orgId)) {
                    queryWrapper.and(i -> i.eq("m.create_org_id", orgId));
                }
            }
            queryWrapper.and(i ->
                    i.eq("m.seal_purpose_id", "0").or().eq("m.seal_purpose_id", "7"));
            queryWrapper.eq("m.data_state_code", 5);
        }
        if (APPLE_APP_INDEX.equals(functionType)) {
            queryWrapper.eq("m.seal_purpose_id", "0");
            queryWrapper.ne("d.data_source_code", "100008");
            queryWrapper.eq("m.create_org_id", orgId);
            queryWrapper.and(i -> i.eq("m.data_state_code", 1).or().eq("m.data_state_code", 3));
        }
        if (APPLE_SUPPLE_APP_INDEX.equals(functionType)) {
            queryWrapper.eq("m.seal_purpose_id", "7");
            queryWrapper.eq("m.create_org_id", orgId);
        }
        queryWrapper.orderBy(true, order, "m.create_time");
    }

    @Override
    public Page<SgSealApproval> getAutSealList(JSONObject jsonObject) {
        QueryWrapper<SgSealApproval> queryWrapper = new QueryWrapper<>();
        getAutFilter(queryWrapper, jsonObject);
        return page(new PageUtils<>(jsonObject), queryWrapper);
    }

    private void getAutFilter(QueryWrapper<SgSealApproval> queryWrapper, JSONObject jsonObject) {
        // 模糊搜索值
        String fuzzyValue = jsonObject.containsKey("fuzzyValue") ? jsonObject.getString("fuzzyValue") : null;
        String authorizationName = jsonObject.containsKey("authorizationName") ? jsonObject.getString("authorizationName") : null;
        String sealName = jsonObject.containsKey("sealName") ? jsonObject.getString("sealName") : null;
        String dataType = jsonObject.containsKey("dataType") ? jsonObject.getString("dataType") : null;
        String orgId = jsonObject.containsKey("orgId") ? jsonObject.getString("orgId") : null;
        //顺序字段
        String sortName = jsonObject.containsKey("sortName") ? jsonObject.getString("sortName") : null;
        //顺序
        boolean order = jsonObject.containsKey("order") ? jsonObject.getBoolean("order") : false;
        if (StringUtils.isNotBlank(authorizationName)) {
            queryWrapper.like("contract_name", authorizationName);
        }
        if (StringUtils.isNotBlank(sealName)) {
            queryWrapper.like("seal_names", sealName);
        }
        if (StringUtils.isNotBlank(dataType)) {
            //待用印只能印章管理员看到
            if (dataType.equals("待用印")) {
                queryWrapper.like("seal_admin_ids", orgId);
                // 不显示已生效印章的授权信息
                // 去seal_approval_detail表查询印章管理员为orgId并且take_effect_name为已用印的seal_id
                List<SgSealApprovalDetail> detailList = sgSealApprovalDetailService.list(new QueryWrapper<SgSealApprovalDetail>().like("seal_admin_id_old", orgId).eq("data_state", "已用印"));
                if (!CollectionUtils.isEmpty(detailList)) {
                    List<String> list = detailList.stream().map(SgSealApprovalDetail::getParentId).collect(Collectors.toList());
                    queryWrapper.notIn("id", list);
                }
            }
            //待生效只能经办人看到
            if (dataType.equals("待生效")) {
                queryWrapper.like("create_org_id", orgId);
//                List<SgSealApprovalDetail> detailList = sgSealApprovalDetailService.list(new QueryWrapper<SgSealApprovalDetail>().like("seal_admin_id_old", orgId).eq("data_state", "待生效"));
//                if (!CollectionUtils.isEmpty(detailList)) {
//                    List<String> list = detailList.stream().map(SgSealApprovalDetail::getParentId).collect(Collectors.toList());
//                    queryWrapper.notIn("id", list);
//                }
            }
            //已生效的印章管理员和经办人可以看到
            if (dataType.equals("已生效")) {
                queryWrapper.and(i -> i.eq("create_org_id", orgId).or().like("seal_admin_ids", orgId));
            }
        }
        queryWrapper.and(wrapper -> wrapper.eq("take_effect_name", dataType));
        // 模糊搜索匹配字段
        String[] cols = {"contract_name", "seal_names"};
        Utils.fuzzyValueQuery(queryWrapper, cols, fuzzyValue);
        queryWrapper.eq("seal_purpose", "授权审批");

        if (StringUtils.isNotBlank(sortName)) {
            queryWrapper.orderBy(true, order, Utils.humpToLine2(sortName));
        } else {
            queryWrapper.orderBy(true, order, "create_time");
        }
    }

    @Override
    public String loginSeal(JSONObject json) {
        String token = withSealUtils.singleLogin();
        return "https://sc.btsteel.com/sign/seal/list?accessToken=" + token + "&externalSystemId=100008";
    }

    @Override
    public boolean pushElectronicSign(JSONObject jsonObject) {
        SgSealApproval sgSealApproval = JSONObject.parseObject(jsonObject.toString(), SgSealApproval.class);
        return withSealUtils.fileImp(sgSealApproval.getContractId(), sgSealApproval);
    }

    @Override
    public boolean pushSignRevoke(JSONObject jsonObject) {
        SgSealApproval sgSealApproval = JSONObject.parseObject(jsonObject.toString(), SgSealApproval.class);
        return withSealUtils.cancel(sgSealApproval.getId());
    }

    @Override
    @Transactional
    public ApiJson passback(JSONObject jsonObject) {
        String stus = jsonObject.getString("stus");
        String backup3 = jsonObject.getString("backup3");
        SgSealApproval sealApproval = getById(backup3);
        if (sealApproval == null) {
            return ApiJson.fail(ApiJson.KEY_CODE, -1).data("msg", "数据不存在！");
        }
        if (sealApproval.getTakeEffectCode() != 5) {
            return ApiJson.fail(ApiJson.KEY_CODE, -1).data("msg", "该数据已处理！");
        }
        if ("3".equals(stus)) {
            Date sealDateB = jsonObject.getDate("sealDateB");
            Date sealDateA = jsonObject.getDate("sealDateA");
            try {
                Date ourDate = "our".equals(sealApproval.getIsSeam()) ? sealDateA : sealDateB;
                Date otherDate = "other".equals(sealApproval.getIsSeam()) ? sealDateA : sealDateB;
                sealApproval.setOtherSealTime(otherDate);
                List<SgSealApprovalDetail> list = sgSealApprovalDetailService.list(new QueryWrapper<SgSealApprovalDetail>().eq("parent_id", sealApproval.getId()));
                for (SgSealApprovalDetail sgSealApprovalDetail : list) {
                    sgSealApprovalDetail.setOurSealTime(ourDate);
                    sgSealApprovalDetail.setSealNumber(sgSealApprovalDetail.getSealNumberOld());
                    sgSealApprovalDetail.setPrintsNumber(sgSealApprovalDetail.getPrintsNumberOld());
                }
                sgSealApprovalDetailService.updateBatchById(list);
                //取最后的时间
                Date latestDate = getLatestDate(sealDateA, sealDateB);
                sealApproval.setEffectiveTime(latestDate);
                sealApproval.setTakeEffectName("已生效");
                sealApproval.setTakeEffectCode(3);
                String strDateFormat = "yyyy年MM月dd日";
                SimpleDateFormat sdf = new SimpleDateFormat(strDateFormat);
                sealApproval.setEffectiveDescription("合同自" + sdf.format(latestDate) + "成立。");
                JSONArray fileObject = jsonObject.getJSONArray("fileObject");
                JSONArray jsonArray = new JSONArray();
                for (Object o : fileObject) {
                    JSONObject jsonText = (JSONObject) o;
                    SgSysDoc sgSysDoc = new SgSysDoc();
                    String sysDocId = Utils.createUUID();
                    sgSysDoc.setId(sysDocId);
                    sgSysDoc.setDocName(jsonText.getString("fileName"));
                    sgSysDoc.setDocType("pdf");
                    sgSysDoc.setIsDoc(1);
                    sgSysDoc.setFileId(jsonText.getString("url"));
                    sgSysDoc.setFilePlace("dzqz/");
                    sgSysDoc.setCreateTime(new Date());
                    JSONObject doc = new JSONObject();
                    doc.put("docId", sysDocId);
                    doc.put("name", jsonText.getString("fileName") + ".pdf");
                    doc.put("status", "success");
                    jsonArray.add(doc);
                    sgSysDocService.insertSelective(sgSysDoc);
                }
                sealApproval.setOriginalAttachment(jsonArray.toString());
                //  设置合同信息和用印
                setBmContractAndSeal(sealApproval);
                boolean b = updateById(sealApproval);
                if (b) {
                    //  更新电子用印信息
                    CooperateWithSeal withSeal = cooperateWithSealService.getOne(new QueryWrapper<CooperateWithSeal>().eq("backup3", backup3).eq("stus", 0));
//                    CooperateWithSeal documentCode = cooperateWithSealService.getById(jsonObject.getString("documentCode"));
                    withSeal.setStus(1);
                    withSeal.setFileObject(fileObject.toJSONString());
                    cooperateWithSealService.updateById(withSeal);
                    return ApiJson.succ("code", 200, "msg", "推送成功！");
                }
            } catch (Exception e) {
                return ApiJson.fail(ApiJson.KEY_CODE, -1).data("msg", "参数格式错误！");
            }
        }
//        else {
//            String sealPartyB = jsonObject.getString("sealPartyB");
//            String sealDateB = jsonObject.getString("sealDateB");
//            if (!isDateValid(sealDateB, "yyyy-MM-dd HH:mm:ss")) {
//                return ApiJson.fail(ApiJson.KEY_CODE, -1).data("msg", "sealPartyB不是日期格式！");
//            }
//            try {
//                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                BmContract bmContract = bmContractService.getById(sealApproval.getContractId());
//                //  判断 sealPartyB 是否等于 bmContract.getOurPartyList()我方

//                if (bmContract.getOtherPartyList().contains(sealPartyB)) {
//
//                    updateById(sealApproval);
//                    return ApiJson.succ("code", 200, "msg", "推送成功！");
//                }
//            } catch (Exception e) {
//                return ApiJson.fail(ApiJson.KEY_CODE, -1).data("msg", "参数格式错误！");
//            }
//        }
        return ApiJson.fail(ApiJson.KEY_CODE, -1).data("msg", "未更新！");
    }

    public Date getLatestDate(Date... dates) {
        return Arrays.stream(dates)
                .filter(Objects::nonNull)
                .max(Comparator.naturalOrder())
                .orElse(null);
    }

    /**
     * 拆分文件名和文件类型
     *
     * @param fileName 完整的文件名
     * @return 包含文件名称和文件类型的数组
     */
    public static String[] splitFileName(String fileName) {
        // 检查文件名是否为空或不包含扩展名
        if (fileName == null || !fileName.contains(".")) {
            throw new IllegalArgumentException("无效的文件名");
        }

        // 使用最后一个点号分割文件名和扩展名
        int lastDotIndex = fileName.lastIndexOf('.');
        String name = fileName.substring(0, lastDotIndex);
        String extension = fileName.substring(lastDotIndex + 1);

        return new String[]{name, extension};
    }

    public static boolean isDateValid(String dateStr, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        try {
            LocalDate.parse(dateStr, formatter);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }
}
