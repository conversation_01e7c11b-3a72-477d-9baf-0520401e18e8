package com.klaw.service.imp.contractServiceImpl.contract;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yitter.idgen.YitIdHelper;
import com.klaw.constant.DataStateBPM;
import com.klaw.dao.contractDao.contract.BmContractPerformMapper;
import com.klaw.entity.contractBean.ContractPerform;
import com.klaw.entity.contractBean.ContractWarn;
import com.klaw.entity.contractBean.SgContractApproval;
import com.klaw.entity.contractBean.contract.*;
import com.klaw.entity.contractBean.contract.vo.BmContractPerformVO;
import com.klaw.service.contractService.contract.*;
import com.klaw.utils.*;
import com.sgai.mcp.job.dto.JobData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类
 * 合同履行计划实现类
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Service
public class BmContractPerformServiceImpl extends ServiceImpl<BmContractPerformMapper, BmContractPerform> implements BmContractPerformService {
    @Autowired
    private BmContractService bmContractService;
    @Autowired
    private BmContractToMainService bmContractToMainService;

    public static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Override
    public BmContractPerform getBmContractPerform(String id) {
        return baseMapper.selectById(id);
    }

    @Override
    public BmContractPerform getBmContractPerformByPerformCode(String performCode) {
        QueryWrapper<BmContractPerform> queryWrapper = new QueryWrapper<BmContractPerform>().eq("perform_code", performCode);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<BmContractPerform> queryByContractCode(String contractCode) {
        QueryWrapper<BmContractPerform> queryWrapper = new QueryWrapper<BmContractPerform>()
                .eq("contract_code", contractCode);
        List<BmContractPerform> list = baseMapper.selectList(queryWrapper);
        BigDecimal bigDecimal = new BigDecimal(0);
        list = list.stream().filter(perform->{
            if (perform.getPerformExecutoryMoney() != null) {
                return (perform.getPerformExecutoryMoney().compareTo(bigDecimal))>0;
            }
            return true;
        }).collect(Collectors.toList());
        return list;
    }


    @Override
    public int modify(BmContractPerform bmContractPerform) {
        if (bmContractPerform.getPlanAmount() != null) {
            bmContractPerform.setPlanRatio(caluateTotalAmount(bmContractPerform));
        }
        return baseMapper.updateById(bmContractPerform);
    }

    @Override
    public void remove(String id) {
        baseMapper.deleteById(id);
    }

    @Override
    public int saveData(BmContractPerform bmContractPerform) {
        List<BmContractPerform> performList = queryByContractId(bmContractPerform.getContractId());
        Optional<Integer> max = performList.stream().map(BmContractPerform::getSerialNumber).max(Comparator.naturalOrder());
        if (max.isPresent()) {
            bmContractPerform.setSerialNumber(max.get() + 1);
        }else {
            bmContractPerform.setSerialNumber(1);
        }
        bmContractPerform.setPerformId(YitIdHelper.nextId() + "");
        if (bmContractPerform.getPlanAmount() != null) {
            bmContractPerform.setPlanRatio(caluateTotalAmount(bmContractPerform));
        }
        bmContractPerform.setDataStateCode(0);
        bmContractPerform.setPerformState(PerformStatusEnum.NOT_PLANNED.getStatus());
        bmContractPerform.setPerformStateDesc(PerformStatusEnum.NOT_PLANNED.getDescription());
        return baseMapper.insert(bmContractPerform);
    }

    @Override
    public List<BmContractPerform> queryByContractId(String contractId) {
        QueryWrapper<BmContractPerform> queryWrapper = new QueryWrapper<BmContractPerform>()
                .eq("contract_id", contractId)
                .orderByAsc("plan_date");
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<BmContractPerform> queryByIdNotPlanAmount(String contractId) {
        QueryWrapper<BmContractPerform> queryWrapper = new QueryWrapper<BmContractPerform>()
                .eq("contract_id", contractId)
                .ne("plan_amount", 0)
                .orderByAsc("plan_date");
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 履行计划提交
     * @param contractId
     */
    @Override
    public void submitPerform(String contractId) {
        List<BmContractPerform> performList = queryByContractId(contractId);
        for (BmContractPerform perform : performList) {
            if("6".equals(perform.getPerformState())){
                continue;
            }
            if (StringUtils.isBlank(perform.getPerformCode())) {
                perform.setPerformCode(this.generatePerformCode(perform.getContractCode(), String.format("%03d", perform.getSerialNumber())));
            }
            perform.setPerformState(PerformStatusEnum.PLANNED.getStatus());
            perform.setPerformStateDesc(PerformStatusEnum.PLANNED.getDescription());

            BigDecimal executedMoney = Optional.ofNullable(perform.getPerformExecutedMoney())
                    .orElse(BigDecimal.ZERO);
            //发布时需要初始化实际待履行金额
            perform.setPerformExecutoryMoney(perform.getPlanAmount().subtract(executedMoney));
            perform.setUpdateTime(new Date());
        }
        this.updateBatchById(performList);
        this.setBmContractPerformStatus(contractId);
        //向主数据推送合同明细
        bmContractToMainService.sendToMainContractDetail(performList.get(0).getContractId());
    }

    @Override
    public boolean createScheduler(ContractWarn contractWarn) {
        String warnPsnId = contractWarn.getWarnPsnId();
        if (contractWarn.isWarn() && StringUtils.isNotBlank(warnPsnId)) {
            List<Integer> list = new ArrayList<Integer>();
            if (StringUtils.isNotBlank(contractWarn.getWarnTime())) {
                if (contractWarn.getWarnTime().indexOf("当天（多选）") != -1) {
                    list.add(0);
                } else if (contractWarn.getWarnTime().indexOf("提前7天") != -1) {
                    list.add(7);
                } else if (contractWarn.getWarnTime().indexOf("提前30天") != -1) {
                    list.add(30);
                } else if (contractWarn.getWarnTime().indexOf("提前90天") != -1) {
                    list.add(90);
                } else if (contractWarn.getWarnTime().indexOf("提前180天") != -1) {
                    list.add(180);
                }
                BmContract bmContract = bmContractService.getById(contractWarn.getRelationId());
                List<BmContractPerform> performList = list(new QueryWrapper<BmContractPerform>().eq("contract_id", contractWarn.getRelationId()));
                if(!CollectionUtils.isEmpty(performList)){
                    for (BmContractPerform contractPerform : performList) {
                        if(!"5".equals(contractPerform.getPerformState()) && contractPerform.getPlanDate() != null){
                            for (Integer integer : list) {
                                long end = contractPerform.getPlanDate().getTime() + integer * 24 * 60 * 60 * 1000;
                                String formatDate=dateFormat.format(end);
                                Date date = null;
                                try {
                                    date = dateFormat.parse(formatDate);
                                } catch (ParseException e) {
                                    System.out.println("printStackTrace异常");
                                }
                                List<JobData> jobParam = new ArrayList<>();
                                JobData jobData1 = new JobData();
                                JobData jobData2 = new JobData();
                                JobData jobData3 = new JobData();
                                JobData jobData4 = new JobData();
                                JobData jobData5 = new JobData();
                                JobData jobData6 = new JobData();
                                JobData jobData7 = new JobData();
                                JobData jobData8 = new JobData();
                                jobData1.setName("content");
                                jobData1.setValue(bmContract.getContractName()+"--"+contractPerform.getPerformText()+"--"+System.currentTimeMillis());
                                jobData1.setValue("距离《" + bmContract.getContractName() + "》-" + contractPerform.getPerformText() + "-" + contractPerform.getSettlementMethod() + "期限还剩下" + integer + "天 ");
                                jobData2.setName("moduleName");
                                jobData2.setValue("合同履行");
                                jobData3.setName("dataId");
                                jobData3.setValue(contractPerform.getPerformId());
                                jobData4.setName("url");
                                jobData4.setValue("perform_index");
                                jobData5.setName("params");
                                jobData5.setValue("");
                                jobData6.setName("createOrgId");
                                jobData6.setValue(contractWarn.getCreateOrgId());
                                jobData7.setName("receiveOrgIds");
                                jobData7.setValue(warnPsnId);
                                jobData8.setName("whetherProcess");
                                jobData8.setValue("true");

                                jobParam.add(jobData1);
                                jobParam.add(jobData2);
                                jobParam.add(jobData3);
                                jobParam.add(jobData4);
                                jobParam.add(jobData5);
                                jobParam.add(jobData6);
                                jobParam.add(jobData7);
                                jobParam.add(jobData8);
                                QuartzUtils.createSimpleJob("com.klaw.scheduler.contract.PerformScheduler", date, null, "1", "1", jobParam, bmContract.getContractName()+"-"+contractPerform.getPerformText()+System.currentTimeMillis(), "合同履行提醒", "");
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 校验履行计划完成情况
     * @param contractId
     * @param state
     * @return
     */
    @Override
    public boolean checkPerformStatus(String contractId, Integer state) {
        if (state!=5){
            return true;
        }
        List<BmContractPerform> performList = this.queryByIdNotPlanAmount(contractId);
        if (performList != null && !performList.isEmpty()) {
            Optional<BmContractPerform> first = performList.stream().filter(perform -> {
                return perform.getPerformProgress().floatValue() != 100;
            }).findFirst();
            return !first.isPresent();
        }
        return true;
    }

    /**
     * 变更履行计划状态
     * @param contractId
     */
    @Override
    public void changePerformStatus(String contractId,String status,String statusDesc) {
        UpdateWrapper<BmContractPerform> updateWrapper = new UpdateWrapper<BmContractPerform>();
        updateWrapper.eq("contract_id",contractId);
        updateWrapper.set("perform_state",status)
                .set("perform_state_desc",statusDesc);
        this.update(updateWrapper);
    }

    @Override
    public PageUtils<BmContractPerformVO> loadPerfomList(JSONObject jsonObject) {
        PageUtils<BmContractPerformVO> pageUtils = new PageUtils<>(jsonObject);
        QueryWrapper<BmContractPerformVO> queryWrapper = new QueryWrapper<BmContractPerformVO>();
        getPerformDataFilter(queryWrapper, jsonObject);
        PageUtils<BmContractPerformVO> page = baseMapper.loadPerfomList(pageUtils, queryWrapper);
        return page;
    }

    @Override
    public List<BmContractPerformVO> loadPerfomDataList(JSONObject jsonObject) {
        QueryWrapper<BmContractPerformVO> queryWrapper = new QueryWrapper<BmContractPerformVO>();
        getPerformDataFilter(queryWrapper, jsonObject);
        return baseMapper.loadPerfomDataList(queryWrapper);
    }

    @Override
    public String generatePerformDataExcel(List<BmContractPerformVO> dataList, String fileName) {
        Map<String, List<BmContractPerformVO>> contractGroup = new HashMap<>();
        for (BmContractPerformVO contract : dataList) {
            contractGroup.computeIfAbsent(contract.getContractCode(), k -> new ArrayList<>()).add(contract);
        }
        // 创建 ExcelWriter 对象
        ExcelWriter writer = ExcelUtil.getWriter(fileName);

        writer.setOnlyAlias(true);
        // 写入数据
        writer.write(dataList, true);
        //自适应列宽
        writer.autoSizeColumnAll();
        writer.setColumnWidth(2,30);
        writer.setColumnWidth(3,30);
        writer.setColumnWidth(6,15);
        writer.setColumnWidth(7,15);
        writer.setColumnWidth(16,30);
        writer.setColumnWidth(19,15);
        writer.setColumnWidth(20,15);
        writer.setColumnWidth(21,15);
       // writer.merge(1, 2, 1, 1, null, true);
        int startRow = 1;
        int endRow = 1;
        // 合并单元格
        for (Map.Entry<String, List<BmContractPerformVO>> entry : contractGroup.entrySet()) {
            int size = entry.getValue().size();
            if(size==1){
                startRow++;
                endRow++;
            }else{
                endRow += size;
                writer.merge(startRow, endRow-1, 0, 0, null, true);
                writer.merge(startRow, endRow-1, 1, 1, null, true);
                writer.merge(startRow, endRow-1, 2, 2, null, true);
                writer.merge(startRow, endRow-1, 3, 3, null, true);
                writer.merge(startRow, endRow-1, 4, 4, null, true);
                writer.merge(startRow, endRow-1, 5, 5, null, true);
                writer.merge(startRow, endRow-1, 6, 6, null, true);
                writer.merge(startRow, endRow-1, 7, 7, null, true);
            }
            System.out.println(entry.getKey()+"----"+size);
            System.out.println(startRow+"--------------"+endRow);
            startRow = endRow;
        }
        // 关闭 writer，释放资源
        writer.close();
        return fileName;
    }

    @Override
    public boolean cancel(String id) {
        BmContractPerform byId = getById(id);
        if (byId != null) {
            byId.setPerformState("6");
            byId.setPerformStateDesc("作废");
            return updateById(byId);
        }
        return false;
    }


    private void getFilter(QueryWrapper<BmContractPerformVO> queryWrapper, JSONObject json) {
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        //顺序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;
        //顺序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;
        // 合同名称
        String contractName = json.containsKey("contractName") ? json.getString("contractName") : null;
        String contractCode = json.containsKey("contractCode") ? json.getString("contractCode") : null;
        String performCode = json.containsKey("performCode") ? json.getString("performCode") : null;
        String payTypeCode = json.containsKey("payTypeCode") ? json.getString("payTypeCode") : null;
        String contractType = json.containsKey("contractType") ? json.getString("contractType") : null;
        String otherPartyName = json.containsKey("otherPartyName") ? json.getString("otherPartyName") : null;
        String ourPartyName = json.containsKey("ourPartyName") ? json.getString("ourPartyName") : null;
        String startTimeMin = json.containsKey("startTimeMin") ? json.getString("startTimeMin") : null;
        String startTimeMax = json.containsKey("startTimeMax") ? json.getString("startTimeMax") : null;

        if (StringUtils.isNotBlank(contractName)) {
            queryWrapper.like("contract.contract_name", contractName);
        }
        if (StringUtils.isNotBlank(contractCode)){
            queryWrapper.like("contract.contract_code",contractCode);
        }
        if (StringUtils.isNotBlank(performCode)){
            queryWrapper.like("perform.perform_code",performCode);
        }
        if (StringUtils.isNotBlank(orgId)){
            queryWrapper.eq("contract.create_org_id", orgId);
        }
        if (StringUtils.isNotBlank(payTypeCode)){
            queryWrapper.eq("contract.revenue_expenditure_code",payTypeCode);
        }
        if (StringUtils.isNotBlank(contractType)){
            queryWrapper.like("contract.contract_type",contractType);
        }
        if (StringUtils.isNotBlank(otherPartyName)){
            queryWrapper.like("contract.other_party_name",otherPartyName);
        }
        if (StringUtils.isNotBlank(ourPartyName)) {
            queryWrapper.like("contract.our_party_name",ourPartyName);
        }
        if (StringUtils.isNotBlank(startTimeMin)){
            queryWrapper.ge("contract.create_time",startTimeMin);
        }
        if (StringUtils.isNotBlank(startTimeMax)){
            queryWrapper.le("contract.create_time",startTimeMax);
        }

        if (StringUtils.isNotBlank(sortName)) {
            queryWrapper.orderBy(true, order, Utils.humpToLine2(sortName));
        } else {
            queryWrapper.orderByDesc("contract.create_time desc,contract.id");
        }
    }

    private void getPerformDataFilter(QueryWrapper<BmContractPerformVO> queryWrapper, JSONObject json) {
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        // 合同名称
        String contractName = json.containsKey("contractName") ? json.getString("contractName") : null;
        String contractCode = json.containsKey("contractCode") ? json.getString("contractCode") : null;
        String performCode = json.containsKey("performCode") ? json.getString("performCode") : null;
        String payTypeCode = json.containsKey("payTypeCode") ? json.getString("payTypeCode") : null;
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

        if (StringUtils.isNotBlank(contractName)) {
            queryWrapper.like("contract.contract_name", contractName);
        }
        if (StringUtils.isNotBlank(contractCode)){
            queryWrapper.like("contract.contract_code",contractCode);
        }
        if (StringUtils.isNotBlank(performCode)){
            queryWrapper.like("perform.perform_code",performCode);
        }
        if (StringUtils.isNotBlank(payTypeCode)){
            queryWrapper.eq("contract.revenue_expenditure_code",payTypeCode);
        }
        if (StringUtils.isNotBlank(fuzzyValue)){
            queryWrapper.and(i-> i.like("contract.contract_name",fuzzyValue)
                    .or().like("contract.contract_code",fuzzyValue)
                    .or().like("contract.our_party_name",fuzzyValue)
                    .or().like("contract.other_party_name",fuzzyValue)
                    .or().like("contract.contract_type",fuzzyValue)
            );
        }
        if (isQuery) {
            String index = "0".equals(payTypeCode)?"perform_pay_index": "perform_receive_pay_index";
            Long functionId = DataAuthUtils.getFunctionIdByCode(index);
            DataAuthUtils.dataPermSql(queryWrapper, "contract.create_psn_full_id", functionId, orgId);
        } else {
            queryWrapper.eq("contract.create_org_id", orgId);
        }

        queryWrapper.orderByDesc("contract.create_time desc,contract.id");
    }

    /**
     * 计划履行比例
     * @param bmContractPerform
     * @return
     */
    private BigDecimal caluateTotalAmount(BmContractPerform bmContractPerform) {
        //查询合同信息
        BmContract contract = bmContractService.getById(bmContractPerform.getContractId());
        if (contract.getAfterChangeMoney() == null && bmContractPerform.getPlanAmount() != null && bmContractPerform.getPlanAmount().compareTo(BigDecimal.ZERO) > 0) {
            return bmContractPerform.getPlanAmount().divide(contract.getContractMoney(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        } else if (contract.getAfterChangeMoney() != null && contract.getAfterChangeMoney().compareTo(BigDecimal.ZERO) > 0
                && bmContractPerform.getPlanAmount() != null && bmContractPerform.getPlanAmount().compareTo(BigDecimal.ZERO) > 0) {
            return bmContractPerform.getPlanAmount().divide(contract.getAfterChangeMoney(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        }
        return BigDecimal.ZERO;
    }

    private String generatePerformCode(String contractCode, String serialNumber) {
        return contractCode + "-" + serialNumber;
    }

    /**
     * 履行计划发布后，修改对应合同的履行计划状态为已计划
     * @param contractId 合同 id
     */
    private void setBmContractPerformStatus(String contractId){
        BmContract bmContract = bmContractService.getById(contractId);
        bmContract.setPerformanceStateCode(DataStateBPM.PERFORMANCE_STATE_5.getKey());
        bmContract.setPerformanceState(DataStateBPM.PERFORMANCE_STATE_5.getValue());
        //计算主合同合同已履行金额
        BigDecimal executedMoney = Optional.ofNullable(bmContract.getContractSuppleExecutedMoney())
                .orElse(BigDecimal.ZERO);
        BigDecimal financialAmount = Optional.ofNullable(bmContract.getFinancialAmount())
                .orElse(BigDecimal.ZERO);
        bmContract.setFulfillmentAmount(executedMoney.add(financialAmount));
        bmContract.setContractExecutoryMoney(calculateExecutoryMoney(bmContract.getAfterChangeMoney(),bmContract.getFulfillmentAmount()));
        bmContractService.updateById(bmContract);
        //向主数据推送合同明细
        bmContractToMainService.sendToMainContractDetail(contractId);
    }

    /**
     * 计算待履行金额
     * @param afterChangeMoney  变更后合同金额
     * @param contractExecutedMoney 已履行金额
     * @return 待履行金额
     */
    public static BigDecimal calculateExecutoryMoney(BigDecimal afterChangeMoney, BigDecimal contractExecutedMoney) {
        // 处理变更后合同金额为null的情况（视为0）
        BigDecimal safeAfterChange = afterChangeMoney != null ? afterChangeMoney : BigDecimal.ZERO;

        // 处理已履行金额为null的情况（视为0）
        BigDecimal safeExecuted = contractExecutedMoney != null ? contractExecutedMoney : BigDecimal.ZERO;

        // 计算待履行金额 = 变更后金额 - 已履行金额
        return safeAfterChange.subtract(safeExecuted);
    }
}


