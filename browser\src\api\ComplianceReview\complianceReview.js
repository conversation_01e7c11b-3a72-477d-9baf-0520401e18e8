import {request} from '@/api/index';

export default {

    query(data){
        return request({
            url:'/ComplianceReview/query',
            method:'post',
            data
        })
    },

    save(data){
        return request({
            url:'/ComplianceReview/save',
            method:'post',
            data
        })
    },

    getOriginalComplianceReview(data) {
        return request({
            url:'/ComplianceReview/getOriginalComplianceReview',
            method:'post',
            data
        })
    },

    queryById(data){
        return request({
            url:'/ComplianceReview/queryById',
            method:'post',
            data:{
                id:data
            }
        })
    },

    deletebyid(data){
        return request({
            url:'/ComplianceReview/deleteById',
            method:'post',
            data
        })
    },

    queryTableData(data){
        return request({
            url:'/ComplianceReview/queryMajorMatter',
            method:'post',
            data
        })
    },

    setParam(data){
        return request({
            url:'/ComplianceReview/setParam',
            method:'post',
            data
        })
    },
        exportRiskLedger(data) {
        return request({
            url: '/ComplianceReview/exportRiskLedger',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
}