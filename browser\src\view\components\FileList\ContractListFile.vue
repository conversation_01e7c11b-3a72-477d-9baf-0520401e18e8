<template>
	<el-col :span="16">
		<li :key="item.docId" v-for="item in items">
			<div v-if="item.suffix == 'doc' || item.suffix == 'docx'" label="合同附件">
				<el-link type="primary" :underline="false" @click="download(item)">{{ item.name }}</el-link>
				<el-button size="mini" type="primary" style="margin-left: 30px" @click="convertToPDF(item)">生成PDF</el-button>
			</div>
			<!--      <el-form-item v-else-if="item.suffix == 'pdf' " label="PDF附件">-->
			<!--        <el-link type="primary" :underline="false" @click="download(item)">{{ item.name }}</el-link>-->
			<!--      </el-form-item>-->
			<div v-else label="其他附件">
				<el-link type="primary" :underline="false" @click="download(item)">{{ item.name }}</el-link>
<!--				<el-button size="mini" type="primary" style="margin-left: 30px" @click="convertToWatermark(item)">生成PDF</el-button>-->
			</div>
		</li>
	</el-col>
</template>

<script>
	import docApi from '@/api/_system/doc';
  import { mapGetters } from 'vuex'

	export default {
		name: 'ContractFile',
		props: {
			myFiles: {
				type: Array,
				default: null,
			},
			parentId: {
				type: String,
				default: '',
			},
		},
		data() {
			return {
				files: [],
				items: [],
				docPath: '/effect',
				// docPath: '/法务管理',
				id: this.utils.createUUID(),
			};
		},
    computed: {
      ...mapGetters(['orgContext'])
    },
		watch: {
			myFiles: {
				handler(newValue, oldValue) {
					if (newValue) {
						let array = [];
						this.files = newValue;
						this.files.forEach(function (data, index) {
							if (data.attachment) {
								JSON.parse(data.attachment).forEach(function (item, index) {
									item['suffix'] = item['name'].match(/[^.]+$/)[0];
									item['textId'] = data['id'];
									array.push(item);
								});
							}
						});
						this.items = array;
					}
				},
				immediate: true,
			},
		},
		methods: {
			download(file) {
        let orgId = this.orgContext.currentOrgId != null ? this.orgContext.currentOrgId : this.orgContext.currentOgnId;
        docApi.getFilePath(file.docId,orgId).then(res => {
          if (res && res.data.data) {
            //this.utils.newWin(res.data.data,'newWin','_blank')
            window.open(res.data.data, '_blank')
          } else {
            this.$message({
              showClose: true,
              message: '预览失败！附件可能已删除，请联系管理员！',
              type: 'warning'
            })
          }
        })
				// docApi
				// 	.download(file.docId)
				// 	.then((res) => {
				// 		if (res) {
				// 			if (res && res.data.data) {
				// 				this.utils.newWin(res.data.data, 'newWin', '_blank');
				// 			} else {
				// 				this.$message({
				// 					showClose: true,
				// 					message: '下载失败！附件可能已删除，请联系管理员！',
				// 					type: 'warning',
				// 				});
				// 			}
				// 		}
				// 	})
				// 	.catch((res) => {});
			},
			// convertToPDF(file) {
			//   // this.$emit("callback", true);
			//   const formData = new FormData()
			//   formData.append('id', this.id)
			//   formData.append('docId', file.docId)
			//   formData.append('docPath', this.docPath)
			//   formData.append('textId', file.textId)
			//   docApi.convertToPDF(formData).then(res => {
			//     // if (res) {
			//     //   this.$emit("callback", false);
			//     //   if (res && res.data.data) {
			//     //     this.$message({
			//     //       showClose: true,
			//     //       message: '转换成功',
			//     //       type: 'success'
			//     //     })
			//     //   } else {
			//     //     this.$message({
			//     //       showClose: true,
			//     //       message: '转换失败！',
			//     //       type: 'warning'
			//     //     })
			//     //   }
			//     // }
			//     // if (res) {
			//     //   const blob = res.data
			//     //   // const fileName = file.name.substring(0, file.name.lastIndexOf(".")) + '.pdf'
			//     //   const fileName = '采购合同.pdf'
			//     //   if ('download' in document.createElement('a')) {
			//     //     // 非IE下载
			//     //     const elink = document.createElement('a')
			//     //     elink.download = fileName
			//     //     elink.style.display = 'none'
			//     //     elink.href = URL.createObjectURL(blob)
			//     //     document.body.appendChild(elink)
			//     //     elink.click()
			//     //     URL.revokeObjectURL(elink.href) // 释放URL 对象
			//     //     document.body.removeChild(elink)
			//     //   } else {
			//     //     // IE10+下载
			//     //     navigator.msSaveBlob(blob, fileName)
			//     //   }
			//     // }
			//     debugger
			//     console.log("下载的文件流",res)
			//     try{
			//       // let blob = new Blob([res.data],{type: 'application/vnd.ms-excel'});    //如果后台返回的不是blob对象类型，先定义成blob对象格式
			//       // let blob = new Blob([res.data], {type: 'application/pdf'});
			//       // 创建一个URL对象
			//       const url = window.URL.createObjectURL(res.data);
			//       // 创建一个a标签并设置其href属性为URL对象
			//       const link = document.createElement('a');
			//       link.href = url;
			//       // 设置下载的文件名
			/*      const fileName = file.name.substring(0, file.name.lastIndexOf(".")) + '.pdf'*/
			//       link.setAttribute('download', fileName);
			//       // 模拟点击a标签进行下载
			//       link.click();
			//       // 释放URL对象
			//       window.URL.revokeObjectURL(url);
			//     }catch (e) {
			//       console.log('下载的文件出错',e)
			//     }
			//   }).catch(res => {
			//   })
			// },
			convertToPDF(file) {
				docApi.convertToPDF(file.docId).then(response => {
					let fileName = decodeURI((response.headers['content-disposition'].split(';')[1].split("="))[1])
					// 创建一个blob对象URL
					const blobUrl = window.URL.createObjectURL(new Blob([response.data]));
					// 创建一个下载链接
					const link = document.createElement('a');
					link.href = blobUrl;
					link.setAttribute('download', fileName); // 设置下载文件名
					document.body.appendChild(link);
					link.click(); // 触发下载
					// 清理
					link.parentNode.removeChild(link);
					window.URL.revokeObjectURL(blobUrl);
				}).catch(error => {
					console.error('Error downloading PDF:', error);
				});
			},
			convertToWatermark(file) {
				const formData = new FormData();
				formData.append('id', this.id);
				formData.append('docId', file.docId);
				formData.append('docPath', this.docPath);
				formData.append('textId', file.textId);
				docApi.convertToWatermark(formData).then((res) => {
					if (res) {
						const blob = res.data;
						const fileName = file.name.substring(0, file.name.lastIndexOf('.')) + '.pdf';
						if ('download' in document.createElement('a')) {
							// 非IE下载
							const elink = document.createElement('a');
							elink.download = fileName;
							elink.style.display = 'none';
							elink.href = URL.createObjectURL(blob);
							document.body.appendChild(elink);
							elink.click();
							URL.revokeObjectURL(elink.href); // 释放URL 对象
							document.body.removeChild(elink);
						} else {
							// IE10+下载
							navigator.msSaveBlob(blob, fileName);
						}
					}
				});
			},
		},
	};
</script>

<style scoped></style>
