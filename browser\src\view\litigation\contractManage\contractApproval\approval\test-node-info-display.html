<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>岗位职责显示测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            margin: 10px 0;
            border-left: 4px solid #409eff;
            white-space: pre-wrap;
        }

        .contract-form {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-top: 20px;
        }

        .table_content {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .th_label_approval {
            background: #f5f7fa;
            padding: 12px;
            border: 1px solid #e9ecef;
            font-weight: 600;
            text-align: center;
            width: 120px;
        }

        .td_value_approval {
            padding: 12px;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div id="app" class="test-container">
        <div class="test-header">
            <h1>岗位职责显示功能测试</h1>
            <p>测试在合同审批单上方显示当前岗位职责的功能</p>
        </div>

        <div class="demo-section">
            <h3>📋 模拟接口返回数据</h3>
            <p>queryById接口返回的currentNodes数据：</p>
            <div class="code-block">{{ JSON.stringify(mockApiResponse.currentNodes, null, 2) }}</div>
            
            <h4>NodeId映射表：</h4>
            <el-table :data="nodeMapData" border style="width: 100%; margin-top: 10px;">
                <el-table-column prop="nodeId" label="Node ID" width="200"></el-table-column>
                <el-table-column prop="nodeName" label="岗位名称" width="200"></el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="selectNode(scope.row.nodeId)">选择此岗位</el-button>
                    </template>
                </el-table-column>
            </el-table>
            
            <div style="margin-top: 20px;">
                <el-button @click="testNodeMapping" type="primary">测试NodeId映射</el-button>
                <el-button @click="simulateApiCall" type="success">模拟接口调用</el-button>
            </div>
        </div>

        <!-- 模拟合同审批单界面 -->
        <div class="contract-form">
            <!-- 岗位职责显示行 -->
            <el-row v-if="currentNodeInfo" style="margin-top: 10px;">
                <el-col :span="24" style="text-align: center;">
                    <el-tag type="info" size="medium" style="padding: 8px 16px; font-size: 14px;">
                        <i class="el-icon-user-solid" style="margin-right: 5px;"></i>
                        当前岗位职责：{{ currentNodeInfo }}
                    </el-tag>
                </el-col>
            </el-row>
            
            <el-row style="margin-top: 20px;">
                <span style="text-align: left;font-size: 23px;margin-left: 43%;font-weight: 900;">新增合同审批单</span>
            </el-row>

            <!-- 模拟表单内容 -->
            <div style="margin-top: 20px;">
                <table class="table_content">
                    <tbody>
                        <tr>
                            <th class="th_label_approval" colspan="3">合同名称</th>
                            <td class="td_value_approval" colspan="9">测试合同名称</td>
                            <th class="th_label_approval" colspan="3">合同编号</th>
                            <td class="td_value_approval" colspan="9">A20000000BG03250800001</td>
                        </tr>
                        <tr>
                            <th class="th_label_approval" colspan="3">合同类型</th>
                            <td class="td_value_approval" colspan="9">其他仓储保管合同</td>
                            <th class="th_label_approval" colspan="3">合同金额</th>
                            <td class="td_value_approval" colspan="9">111.00元</td>
                        </tr>
                        <tr>
                            <th class="th_label_approval" colspan="3">甲方</th>
                            <td class="td_value_approval" colspan="9">包头钢铁（集团）有限责任公司</td>
                            <th class="th_label_approval" colspan="3">乙方</th>
                            <td class="td_value_approval" colspan="9">包头市龙兴业路桥工程有限责任公司</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 测试结果显示 -->
        <div class="demo-section" v-if="testResult">
            <h3>🔍 测试结果</h3>
            <div class="code-block">{{ testResult }}</div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                currentNodeInfo: null,
                testResult: '',
                // 模拟接口返回数据
                mockApiResponse: {
                    currentNodes: [
                        {
                            "processInstanceId": "3292654",
                            "processDefinitionId": "BGJT_HTQC_01:13:3292653",
                            "name": "三级审批",
                            "startTime": "2025-08-25 21:53:42",
                            "processDefinitionName": "BGJT_HTQC_01:13:3292653",
                            "assignee": "60019370",
                            "endTime": "2025-08-25 23:11:26",
                            "nodeId": "Activity_1r1du0j",
                            "taskId": "3292741"
                        },
                        {
                            "processInstanceId": "3292654",
                            "processDefinitionId": "BGJT_HTQC_01:13:3292653",
                            "name": "三级审批",
                            "startTime": "2025-08-25 21:53:41",
                            "processDefinitionName": "BGJT_HTQC_01:13:3292653",
                            "assignee": "10133",
                            "endTime": "2025-08-25 21:53:42",
                            "nodeId": "Activity_0u0241c",
                            "taskId": "3292696"
                        }
                    ]
                },
                // NodeId映射表数据
                nodeMapData: [
                    { nodeId: 'Activity_1rmp80v', nodeName: '业务部门负责人' },
                    { nodeId: 'Activity_1yc9eu3', nodeName: '运改部审核' },
                    { nodeId: 'Activity_1upb5zy', nodeName: '税务审核' },
                    { nodeId: 'Activity_0hgi73c', nodeName: '资金审核' },
                    { nodeId: 'Activity_0no6qkt', nodeName: '财务部部长' },
                    { nodeId: 'Activity_1qs8r6i', nodeName: '法务部风控' },
                    { nodeId: 'Activity_1lee3nx', nodeName: '办公室' },
                    { nodeId: 'Activity_1umzmjb', nodeName: '公司领导' },
                    { nodeId: 'Activity_0wn3tir', nodeName: '返回经办人' },
                    { nodeId: 'Activity_0y3xjh6', nodeName: '法务承办人' },
                    { nodeId: 'Activity_1e2ebp6', nodeName: '合同专业负责人' },
                    { nodeId: 'Activity_0pdswu8', nodeName: '法务部部长' },
                    { nodeId: 'Activity_0pz4x4e', nodeName: '首席合规官' },
                    { nodeId: 'Activity_1r1du0j', nodeName: '三级审批' },
                    { nodeId: 'Activity_0u0241c', nodeName: '三级审批' }
                ]
            },
            mounted() {
                // 模拟页面加载时获取当前节点信息
                this.simulateApiCall();
            },
            methods: {
                // 根据nodeId获取岗位名称
                getNodeNameByNodeId(nodeId) {
                    const nodeMap = {
                        'Activity_1rmp80v': '业务部门负责人',
                        'Activity_1yc9eu3': '运改部审核',
                        'Activity_1upb5zy': '税务审核',
                        'Activity_0hgi73c': '资金审核',
                        'Activity_0no6qkt': '财务部部长',
                        'Activity_1qs8r6i': '法务部风控',
                        'Activity_1lee3nx': '办公室',
                        'Activity_1umzmjb': '公司领导',
                        'Activity_0wn3tir': '返回经办人',
                        'Activity_0y3xjh6': '法务承办人',
                        'Activity_1e2ebp6': '合同专业负责人',
                        'Activity_0pdswu8': '法务部部长',
                        'Activity_0pz4x4e': '首席合规官',
                        'Activity_1r1du0j': '三级审批',
                        'Activity_0u0241c': '三级审批'
                    };
                    
                    return nodeMap[nodeId] || '未知岗位';
                },
                
                // 选择特定岗位进行测试
                selectNode(nodeId) {
                    this.currentNodeInfo = this.getNodeNameByNodeId(nodeId);
                    this.$message.success(`已选择岗位: ${this.currentNodeInfo}`);
                },
                
                // 测试NodeId映射功能
                testNodeMapping() {
                    const testResults = [];
                    this.nodeMapData.forEach(item => {
                        const mappedName = this.getNodeNameByNodeId(item.nodeId);
                        testResults.push({
                            nodeId: item.nodeId,
                            expected: item.nodeName,
                            actual: mappedName,
                            match: item.nodeName === mappedName
                        });
                    });
                    
                    this.testResult = `NodeId映射测试结果:\n${JSON.stringify(testResults, null, 2)}`;
                    
                    const allMatch = testResults.every(r => r.match);
                    if (allMatch) {
                        this.$message.success('所有NodeId映射测试通过！');
                    } else {
                        this.$message.error('部分NodeId映射测试失败！');
                    }
                },
                
                // 模拟接口调用
                simulateApiCall() {
                    this.$message.info('模拟调用queryById接口...');
                    
                    // 模拟异步接口调用
                    setTimeout(() => {
                        const currentNodes = this.mockApiResponse.currentNodes;
                        if (currentNodes && currentNodes.length > 0) {
                            const nodeId = currentNodes[0].nodeId;
                            this.currentNodeInfo = this.getNodeNameByNodeId(nodeId);
                            
                            this.testResult = `模拟接口调用结果:
接口返回的第一个节点: ${JSON.stringify(currentNodes[0], null, 2)}
提取的nodeId: ${nodeId}
映射的岗位名称: ${this.currentNodeInfo}`;
                            
                            this.$message.success(`获取到当前岗位: ${this.currentNodeInfo}`);
                        } else {
                            this.testResult = '接口返回数据中没有currentNodes信息';
                            this.$message.warning('未获取到节点信息');
                        }
                    }, 1000);
                }
            }
        });
    </script>
</body>
</html>
