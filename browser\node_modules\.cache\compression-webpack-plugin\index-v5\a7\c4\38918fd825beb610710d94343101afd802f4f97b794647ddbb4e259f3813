
7fd448163090ac893c3e95b6ef71cd25d47f0dd2	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"305.35f2ae7e52f198ce445e.hot-update.js\",\"contentHash\":\"c2277030c05f0824daf7e685b60e8d9f\"}","integrity":"sha512-/fQYEm5N3kNyV3LySvzD89pbrXeKJfbdwpql6yQ4bmIM7JZ3TGmf/BfNC575DzsSNsQdonxyvY2u0AbVTgPbvg==","time":1756904647346,"size":58766}