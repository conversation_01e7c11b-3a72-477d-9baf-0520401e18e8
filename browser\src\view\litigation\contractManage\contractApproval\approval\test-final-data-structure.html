<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>最终数据结构测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            margin: 10px 0;
            border-left: 4px solid #409eff;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .contract-form {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-top: 20px;
        }

        .success-info {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 13px;
        }

        .step-info {
            background: #e2e3e5;
            border: 1px solid #d6d8db;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="app" class="test-container">
        <div class="test-header">
            <h1>最终数据结构测试</h1>
            <p>测试完整的接口返回数据结构处理</p>
        </div>

        <div class="demo-section">
            <h3>📋 完整的接口返回数据结构</h3>
            <p>实际的queryById接口返回数据：</p>
            <div class="code-block">{{ JSON.stringify(fullApiResponse.data.currentNodes, null, 2) }}</div>
            
            <h4>数据路径分析：</h4>
            <div class="step-info">
                <strong>数据路径：</strong> res.data.currentNodes[0]<br>
                <strong>关键字段：</strong><br>
                - name: "{{ fullApiResponse.data.currentNodes[0].name }}"<br>
                - nameId: "{{ fullApiResponse.data.currentNodes[0].nameId }}"<br>
                - nodeId: "{{ fullApiResponse.data.currentNodes[0].nodeId }}"
            </div>
            
            <div style="margin-top: 20px;">
                <el-button @click="testFinalLogic" type="primary">测试最终处理逻辑</el-button>
                <el-button @click="simulateFullProcess" type="success">模拟完整流程</el-button>
            </div>
        </div>

        <!-- 处理步骤显示 -->
        <div class="demo-section" v-if="processSteps.length > 0">
            <h3>🔍 处理步骤</h3>
            <div v-for="(step, index) in processSteps" :key="index" class="step-info">
                <strong>步骤{{ index + 1 }}:</strong> {{ step }}
            </div>
        </div>

        <!-- 最终结果显示 -->
        <div class="demo-section" v-if="finalResult">
            <h3>✅ 最终结果</h3>
            <div class="success-info">{{ finalResult }}</div>
        </div>

        <!-- 模拟合同审批单界面 -->
        <div class="contract-form">
            <!-- 岗位职责显示行 -->
            <el-row v-if="currentNodeInfo" style="margin-top: 10px;">
                <el-col :span="24" style="text-align: center;">
                    <el-tag type="info" size="medium" style="padding: 8px 16px; font-size: 14px;">
                        <i class="el-icon-user-solid" style="margin-right: 5px;"></i>
                        当前岗位职责：{{ currentNodeInfo }}
                    </el-tag>
                </el-col>
            </el-row>
            
            <el-row style="margin-top: 20px;">
                <span style="text-align: left;font-size: 23px;margin-left: 43%;font-weight: 900;">新增合同审批单</span>
            </el-row>

            <!-- 模拟表单内容 -->
            <div style="margin-top: 20px; padding: 20px; border: 1px solid #e9ecef; border-radius: 4px;">
                <p><strong>合同名称：</strong>{{ fullApiResponse.data.data.contractName }}</p>
                <p><strong>合同编号：</strong>{{ fullApiResponse.data.data.contractCode }}</p>
                <p><strong>合同类型：</strong>{{ fullApiResponse.data.data.contractType }}</p>
                <p><strong>合同金额：</strong>{{ fullApiResponse.data.data.contractMoney }}元</p>
                <p><strong>甲方：</strong>{{ fullApiResponse.data.data.ourPartyName }}</p>
                <p><strong>乙方：</strong>{{ fullApiResponse.data.data.otherPartyName }}</p>
                <p><strong>审批状态：</strong>{{ fullApiResponse.data.data.dataState }}</p>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                currentNodeInfo: null,
                processSteps: [],
                finalResult: '',
                // 完整的接口返回数据（根据您提供的真实数据）
                fullApiResponse: {
                    "data": {
                        "msg": "ok",
                        "code": 1,
                        "currentNodes": [
                            {
                                "processInstanceId": "3305007",
                                "processDefinitionId": "JTHTSP:1:3305006",
                                "name": "业务部门负责人",
                                "nameId": "Activity_1rmp80v",
                                "startTime": "2025-08-26 08:32:50",
                                "processDefinitionName": "JTHTSP:1:3305006",
                                "assignee": "60019370",
                                "endTime": "2025-08-26 08:32:54",
                                "nodeId": "Activity_0u0241c",
                                "taskId": "3305049"
                            }
                        ],
                        "data": {
                            "contractName": "测试0826",
                            "contractCode": "A20000007BG02250800001",
                            "contractType": "仓储合同",
                            "contractMoney": 1111,
                            "ourPartyName": "内蒙古新联信息产业有限公司",
                            "otherPartyName": "包头市龙兴业路桥工程有限责任公司",
                            "dataState": "审批中"
                        }
                    }
                }
            },
            mounted() {
                // 页面加载时自动测试
                this.simulateFullProcess();
            },
            methods: {
                // 根据nodeId获取岗位名称（与实际代码保持一致）
                getNodeNameByNodeId(nodeId) {
                    const nodeMap = {
                        'Activity_1rmp80v': '业务部门负责人',
                        'Activity_1yc9eu3': '运改部审核',
                        'Activity_1upb5zy': '税务审核',
                        'Activity_0hgi73c': '资金审核',
                        'Activity_0no6qkt': '财务部部长',
                        'Activity_1qs8r6i': '法务部风控',
                        'Activity_1lee3nx': '办公室',
                        'Activity_1umzmjb': '公司领导',
                        'Activity_0wn3tir': '返回经办人',
                        'Activity_0y3xjh6': '法务承办人',
                        'Activity_1e2ebp6': '合同专业负责人',
                        'Activity_0pdswu8': '法务部部长',
                        'Activity_0pz4x4e': '首席合规官',
                        'Activity_1r1du0j': '三级审批',
                        'Activity_0u0241c': '三级审批'
                    };
                    
                    return nodeMap[nodeId] || `未知岗位(${nodeId})`;
                },
                
                // 测试最终处理逻辑
                testFinalLogic() {
                    this.processSteps = [];
                    this.finalResult = '';
                    
                    const res = this.fullApiResponse;
                    
                    this.processSteps.push('开始处理接口返回数据...');
                    this.processSteps.push(`检查数据结构: res.data = ${res.data ? '存在' : '不存在'}`);
                    
                    if (res && res.data && res.data.currentNodes && res.data.currentNodes.length > 0) {
                        this.processSteps.push('✅ 数据结构检查通过');
                        
                        const currentNode = res.data.currentNodes[0];
                        this.processSteps.push(`获取第一个节点: ${JSON.stringify(currentNode, null, 2)}`);
                        
                        // 优先使用name字段
                        if (currentNode.name) {
                            this.currentNodeInfo = currentNode.name;
                            this.processSteps.push(`✅ 使用name字段: "${currentNode.name}"`);
                            this.finalResult = `成功获取岗位职责: "${this.currentNodeInfo}" (来源: name字段)`;
                        } else if (currentNode.nameId) {
                            this.currentNodeInfo = this.getNodeNameByNodeId(currentNode.nameId);
                            this.processSteps.push(`✅ 使用nameId映射: "${currentNode.nameId}" -> "${this.currentNodeInfo}"`);
                            this.finalResult = `成功获取岗位职责: "${this.currentNodeInfo}" (来源: nameId映射)`;
                        } else if (currentNode.nodeId) {
                            this.currentNodeInfo = this.getNodeNameByNodeId(currentNode.nodeId);
                            this.processSteps.push(`✅ 使用nodeId映射: "${currentNode.nodeId}" -> "${this.currentNodeInfo}"`);
                            this.finalResult = `成功获取岗位职责: "${this.currentNodeInfo}" (来源: nodeId映射)`;
                        } else {
                            this.processSteps.push('❌ 所有字段都为空，无法获取岗位信息');
                            this.finalResult = '获取岗位职责失败: 所有相关字段都为空';
                        }
                    } else {
                        this.processSteps.push('❌ 数据结构检查失败');
                        this.finalResult = '获取岗位职责失败: 数据结构不正确';
                    }
                    
                    this.$message.success('测试完成');
                },
                
                // 模拟完整流程（与实际代码逻辑完全一致）
                simulateFullProcess() {
                    this.processSteps = [];
                    this.finalResult = '';
                    
                    // 模拟接口调用
                    this.processSteps.push('模拟调用 contractApi.queryById...');
                    
                    setTimeout(() => {
                        const res = this.fullApiResponse;
                        this.processSteps.push('接口调用成功，开始处理返回数据');
                        
                        console.log('queryById接口返回数据:', res);
                        this.processSteps.push('控制台输出: queryById接口返回数据');
                        
                        // 根据实际返回的数据结构，currentNodes在res.data.currentNodes中
                        if (res && res.data && res.data.currentNodes && res.data.currentNodes.length > 0) {
                            const currentNode = res.data.currentNodes[0];
                            console.log('当前节点信息:', currentNode);
                            this.processSteps.push('控制台输出: 当前节点信息');
                            
                            // 优先使用name字段，如果没有则使用nameId映射
                            if (currentNode.name) {
                                this.currentNodeInfo = currentNode.name;
                                console.log('使用name字段:', currentNode.name);
                                this.processSteps.push(`✅ 使用name字段: "${currentNode.name}"`);
                            } else if (currentNode.nameId) {
                                this.currentNodeInfo = this.getNodeNameByNodeId(currentNode.nameId);
                                console.log('使用nameId映射:', currentNode.nameId, '->', this.currentNodeInfo);
                                this.processSteps.push(`✅ 使用nameId映射: "${currentNode.nameId}" -> "${this.currentNodeInfo}"`);
                            } else if (currentNode.nodeId) {
                                this.currentNodeInfo = this.getNodeNameByNodeId(currentNode.nodeId);
                                console.log('使用nodeId映射:', currentNode.nodeId, '->', this.currentNodeInfo);
                                this.processSteps.push(`✅ 使用nodeId映射: "${currentNode.nodeId}" -> "${this.currentNodeInfo}"`);
                            }
                            
                            console.log('最终设置的岗位职责信息:', this.currentNodeInfo);
                            this.processSteps.push('控制台输出: 最终设置的岗位职责信息');
                            
                            this.finalResult = `🎉 岗位职责显示成功: "${this.currentNodeInfo}"`;
                            this.$message.success(`岗位职责获取成功: ${this.currentNodeInfo}`);
                        } else {
                            console.log('未找到currentNodes数据，检查数据结构:', res);
                            this.processSteps.push('❌ 未找到currentNodes数据');
                            this.finalResult = '❌ 获取岗位职责失败: 数据结构不正确';
                            this.$message.error('数据结构检查失败');
                        }
                    }, 1000);
                }
            }
        });
    </script>
</body>
</html>
