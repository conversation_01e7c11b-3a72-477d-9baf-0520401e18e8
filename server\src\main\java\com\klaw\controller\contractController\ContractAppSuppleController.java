package com.klaw.controller.contractController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.klaw.entity.contractBean.contract.BmContract;
import com.klaw.service.contractService.contract.BmContractService;
import com.klaw.utils.ExcelReader;
import com.klaw.utils.ExcelUtils;
import com.klaw.utils.StringUtil;
import com.klaw.vo.Json;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 补录合同controller
 *
 * <AUTHOR>
 * @since 2024/11/12
 */
@RestController
@RequestMapping("contractAppSupple")
public class ContractAppSuppleController {
    @Autowired
    private BmContractService bmContractService;

    @Autowired
    private ResourceLoader resourceLoader;
    /**
     * 补录合同方法提交
     *
     * @param body
     * @return
     */
    @PostMapping("/submit")
//    @Transactional(rollbackFor = Exception.class)
    public Json submit(@RequestBody String body) {
        BmContract bmContract = JSONObject.parseObject(body, BmContract.class);
        return Json.succ().data(bmContractService.saveData(bmContract));
    }

    /**
     * 合同生效
     *
     * @param body
     * @return
     */
    @PostMapping("/takeEffectContract")
    public Json takeEffect(@RequestBody String body) {
        JSONArray jsonArray = JSONObject.parseArray(body);
        bmContractService.takeEffectContract(jsonArray);
        return Json.succ().data("生效成功！");
    }

    @PostMapping("/queryForSuppleDialog")
    public Json queryDialog(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        return Json.succ().data(bmContractService.queryForSuppleDialog(jsonObject));
    }

    /**
     * 下载模版方法
     *
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping("/downloadTemplate")
    public ResponseEntity<InputStreamResource> downloadTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Resource resource = resourceLoader.getResource("classpath:template/caseSuppleTemplate.xlsx");
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=caseSuppleTemplate.xlsx");
        InputStreamResource isr = new InputStreamResource(resource.getInputStream());
        return ResponseEntity.ok().headers(headers).contentType(MediaType.APPLICATION_OCTET_STREAM).body(isr);
    }


    /**
     * 导入合同方法
     *
     * @return
     */
    @PostMapping("/importContractSuppleData")
    //*合同名称	*合同类型	*立项决策	*相对方确认方式	*自定义编码	自定义编码	*我方签约主体	我方地位
    public Json importContractSuppleData(@RequestParam("file") MultipartFile file, @RequestParam String orgId, HttpServletRequest request) throws Exception {
        System.out.println(file.getInputStream());
//        ExcelReader reader = ExcelUtil.getReader(file.getInputStream(), "导入模版");
//        List<Map<String, Object>> dataList = reader.readAll();
        List<Map<String, Object>> dataList = ExcelReader.readExcel(file.getInputStream(),"导入模版");
        //默认删除第一列
        dataList.remove(0);
        dataList = dataList.stream().filter(data->{
            return !"".equals(data.get("*合同名称")) && !data.isEmpty();
        }).collect(Collectors.toList());
        dataList.forEach(data->{
            if (data.containsKey("*增值税率")) {
                Object o = data.get("*增值税率");
                if (!StringUtil.emptyStr(o).isEmpty()) {
                    if("不征税".equals(data.get("*增值税率").toString()) || "免征".equals(data.get("*增值税率").toString())){
                        data.put("*增值税率", data.get("*增值税率").toString());
                        return;
                    }
                    if (!o.toString().contains("%") && Float.parseFloat(o.toString()) < 1) {
                        int val = (int) (Float.parseFloat(data.get("*增值税率").toString()) * 100);
                        data.put("*增值税率", val + "%");
                    }
                }
            }
        });
        List<Map<String, Object>> errorList = bmContractService.checkSuppleDataList(dataList);
        if (!errorList.isEmpty()) {
            return Json.fail("导入失败，数据格式不规范").data(errorList);
        }
        bmContractService.importSuppleDataList(dataList, orgId);
        return Json.succ("导入成功！");
    }

    /**
     * 导出补录合同中错误日志
     *
     * @param body
     * @param response
     */
    @PostMapping("exportImportSuppleData")
    public ResponseEntity<InputStreamResource> exportImportSuppleData(@RequestBody String body, HttpServletResponse response) throws IOException {
        String oper = "download file";
        JSONArray jsonArray = JSONObject.parseArray(body);
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (Object obj : jsonArray) {
            JSONObject jsonObj = (JSONObject) obj;
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("index", jsonObj.getString("index"));
            dataMap.put("errorText", jsonObj.getString("errorText"));
            dataList.add(dataMap);
        }
        long l = System.currentTimeMillis();
        String outputFilePath = System.getProperties().getProperty("user.dir") + File.separator + l + ".xlsx";
        LinkedHashMap<String, String> headMap = new LinkedHashMap<>();
        headMap.put("index", "错误行号");
        headMap.put("errorText", "错误日志");
        HashMap<Integer, Integer> columnWidthMap = new HashMap<>();
        columnWidthMap.put(0, 10);
        columnWidthMap.put(1, 200);
        ExcelUtils.writeExcelData(outputFilePath, headMap, dataList, columnWidthMap);
//        ExcelUtils.downloadFile(outputFilePath,response);
//        ExcelUtils.deleteFile(outputFilePath);
        File file = new File(outputFilePath);
        Resource resources = new FileSystemResource(new File(outputFilePath));
        InputStreamResource isr = new InputStreamResource(resources.getInputStream());
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + file.getName());
        return ResponseEntity.ok().headers(headers).contentType(MediaType.APPLICATION_OCTET_STREAM).body(isr);
    }


}
