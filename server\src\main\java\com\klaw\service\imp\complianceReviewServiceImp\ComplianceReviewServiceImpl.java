package com.klaw.service.imp.complianceReviewServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.constant.DataStateBPM;
import com.klaw.dao.complianceReviewDao.ComplianceReviewMapper;
import com.klaw.entity.complianceReviewBean.ComplianceExamination;
import com.klaw.entity.complianceReviewBean.ComplianceReview;
import com.klaw.entity.complianceReviewBean.ComplianceReviewFixedItem;
import com.klaw.service.complianceReviewService.ComplianceExaminationService;
import com.klaw.service.complianceReviewService.ComplianceReviewFixedItemService;
import com.klaw.service.complianceReviewService.ComplianceReviewService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.utils.Utils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【compliance_review(合规审查表)】的数据库操作Service实现
 * @createDate 2024-11-26 18:23:57
 */
@Slf4j
@Service
public class ComplianceReviewServiceImpl extends ServiceImpl<ComplianceReviewMapper, ComplianceReview>
        implements ComplianceReviewService {

    @Resource
    private ComplianceReviewFixedItemService complianceReviewFixedItemService;

    @Resource
    private ComplianceExaminationService complianceExaminationService;

    @Override
    public Page<ComplianceReview> queryPageData(JSONObject jsonObject) {
        QueryWrapper<ComplianceReview> queryWrapper = new QueryWrapper<>();
        getFilter(jsonObject, queryWrapper);
        return page(new PageUtils<>(jsonObject), queryWrapper);
    }

    @Override
    public ComplianceReview queryDataById(String id) {
        ComplianceReview complianceReview = getById(id);

        List<ComplianceReviewFixedItem> cReviewFixedItems = complianceReviewFixedItemService.list(new QueryWrapper<ComplianceReviewFixedItem>().eq("parent_id", id));
        if (!CollectionUtils.isEmpty(cReviewFixedItems)) {
            complianceReview.setCheckList(cReviewFixedItems);
        }

        List<ComplianceExamination> complianceExaminations = complianceExaminationService.list(new QueryWrapper<ComplianceExamination>().eq("parent_id", id).orderByAsc("sort"));
        if (!CollectionUtils.isEmpty(complianceExaminations)) {
            complianceReview.setExaminationLists(complianceExaminations);
        }
        return complianceReview;
    }

    @Override
    public Page<ComplianceReview> queryMajorMatter(JSONObject jsonObject) {
        String orgId = jsonObject.containsKey("orgId") ? jsonObject.getString("orgId") : null;
        QueryWrapper<ComplianceReview> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("data_state", DataStateBPM.FINISH.getValue())
                .and(wrapper -> wrapper.eq("create_org_id", orgId))
                .and(wrapper -> wrapper.eq("review_category", "HG-SCLX-ZDSX"));
        queryWrapper.orderByDesc("submission_date");
        return page(new PageUtils<>(jsonObject), queryWrapper);
    }

    @Override
    @Transactional
    public void saveData(ComplianceReview complianceReview) {
        complianceReview.setSubmitter(complianceReview.getCreatePsnName());
        complianceReview.setSubmissionDate(complianceReview.getCreateTime());
        complianceReview.setSubmittingUnit(complianceReview.getCreateOrgName());
        // 如果是合同合规并且关联了重大事项，将状态改为审批完成
        if ("HG-SCLX-HTHG".equals(complianceReview.getReviewCategory()) && complianceReview.getRelatedSignificantReview() != null) {
            complianceReview.setDataState(DataStateBPM.FINISH.getValue());
            complianceReview.setDataStateCode(DataStateBPM.FINISH.getKey());
        }
        List<ComplianceReviewFixedItem> fixedItemList = complianceReview.getCheckList();
        if (!CollectionUtils.isEmpty(fixedItemList)) {
            Utils.saveChilds(fixedItemList, "parent_id", complianceReview.getId(), complianceReviewFixedItemService);
        }

        List<ComplianceExamination> cExaminations = complianceReview.getExaminationLists();
        if (!CollectionUtils.isEmpty(cExaminations)) {
            Utils.saveChilds(cExaminations, "parent_id", complianceReview.getId(), complianceExaminationService);
        }
        saveOrUpdate(complianceReview);
    }

    @Override
    public Page<ComplianceReview> getOriginalComplianceReview(JSONObject jsonObject) {
        QueryWrapper<ComplianceReview> queryWrapper = new QueryWrapper<>();
        getOriginalFilter(jsonObject, queryWrapper);
        return page(new PageUtils<>(jsonObject), queryWrapper);
    }

    private void getOriginalFilter(JSONObject jsonObject, QueryWrapper<ComplianceReview> queryWrapper) {
        String fuzzyValue = jsonObject.containsKey("fuzzyValue") ? jsonObject.getString("fuzzyValue") : null;
        String orgId = jsonObject.containsKey("orgId") ? jsonObject.getString("orgId") : null;

        //按送审时间倒序展示
        queryWrapper.orderByDesc("submission_date");
        // 模糊搜索匹配字段
        String[] cols = {"review_subject", "review_category", "submitting_unit"};
        Utils.fuzzyValueQuery(queryWrapper, cols, fuzzyValue);
        // 当前用户发起过并且审批通过的重大决策事项合规审查数据
        queryWrapper
                .eq("create_org_id", orgId)
                .and(wrapper -> wrapper.eq("data_state", DataStateBPM.FINISH.getValue()))
                .and(wrapper -> wrapper.eq("review_category", "HG-SCLX-ZDSX"));
        queryWrapper.orderBy(true, false, "submission_date");
    }

    private void getFilter(JSONObject jsonObject, QueryWrapper<ComplianceReview> queryWrapper) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = jsonObject.containsKey("isQuery") ? jsonObject.getBoolean("isQuery") : false;
        String reviewSubject = jsonObject.containsKey("reviewSubject") ? jsonObject.getString("reviewSubject") : null;
        String reviewCategory = jsonObject.containsKey("reviewCategory") ? jsonObject.getString("reviewCategory") : null;
        String submittingUnit = jsonObject.containsKey("submittingUnit") ? jsonObject.getString("submittingUnit") : null;
        String submitter = jsonObject.containsKey("submitter") ? jsonObject.getString("submitter") : null;
        Date submissionDateStart = jsonObject.containsKey("submissionDateStart") ? jsonObject.getDate("submissionDateStart") : null;
        Date submissionDateEnd = jsonObject.containsKey("submissionDateEnd") ? jsonObject.getDate("submissionDateEnd") : null;
        String dataState = jsonObject.containsKey("dataState") ? jsonObject.getString("dataState") : null;
        String businessArea = jsonObject.containsKey("businessArea") ? jsonObject.getString("businessArea") : null;
        String fuzzyValue = jsonObject.containsKey("fuzzyValue") ? jsonObject.getString("fuzzyValue") : null;
        //顺序字段
        String sortName = jsonObject.containsKey("sortName") ? jsonObject.getString("sortName") : null;
        String orgId = jsonObject.containsKey("orgId") ? jsonObject.getString("orgId") : null;
        String createOgnName = jsonObject.containsKey("createOgnName") ? jsonObject.getString("createOgnName") : null;
        //顺序
        boolean order = jsonObject.containsKey("order") ? jsonObject.getBoolean("order") : false;
        if (StringUtils.isNotBlank(reviewSubject)) {
            queryWrapper.like("review_subject", reviewSubject);
        }
        if (StringUtils.isNotBlank(reviewCategory)) {
            queryWrapper.like("review_category", reviewCategory);
        }
        if (StringUtils.isNotBlank(submittingUnit)) {
            queryWrapper.like("submitting_unit", submittingUnit);
        }
        if (StringUtils.isNotBlank(submitter)) {
            queryWrapper.like("submitter", submitter);
        }
        if (submissionDateStart != null) {
            queryWrapper.ge("submission_date", submissionDateStart);
        }
        if (submissionDateEnd != null) {
            queryWrapper.le("submission_date", submissionDateEnd);
        }
        if (isQuery){
            queryWrapper.ne("data_state_code", DataStateBPM.SAVE.getKey());
            if (StringUtils.isNotBlank(dataState))
                queryWrapper.eq("data_state", dataState);
        }
        if (StringUtils.isNotBlank(createOgnName)) {
            queryWrapper.eq("create_ogn_name", createOgnName);
        }
        if (StringUtils.isNotBlank(businessArea)) {
            queryWrapper.eq("business_area", businessArea);
        }

        //台账权限
        if (isQuery){
            Long functionId = DataAuthUtils.getFunctionIdByCode("hgsc_tz");
            DataAuthUtils.dataPermSql(queryWrapper, null, functionId, orgId);
        }else {
            //经办人权限隔离
            queryWrapper.eq("create_org_id", orgId);
        }

        //按送审时间倒序展示
//        queryWrapper.orderByDesc("submission_date");
        // 模糊搜索匹配字段
        String[] cols = {"review_subject", "review_category", "submitting_unit"};
        Utils.fuzzyValueQuery(queryWrapper, cols, fuzzyValue);

        if (StringUtils.isNotBlank(sortName)) {
            queryWrapper.orderBy(true, order, Utils.humpToLine2(sortName));
        } else {
            queryWrapper.orderBy(true, order, "create_time");
        }
    }

    /**
     * 导出合规审查台账
     *
     * @param json     查询条件
     * @param response HTTP响应对象
     */

    public void exportRiskLedger(JSONObject json, HttpServletResponse response) {
        QueryWrapper<ComplianceReview> queryWrapper = new QueryWrapper<>();

        // 确保导出时设置isQuery为true，获取台账权限数据
        json.put("isQuery", true);
        getFilter(json, queryWrapper);

        // 打印查询SQL用于调试
        log.info("导出查询SQL: {}", queryWrapper.getSqlSegment());

        List<ComplianceReview> complianceReviewList = list(queryWrapper);

        log.info("导出合规审查台账，查询到 {} 条数据", complianceReviewList.size());

        // 如果没有数据，尝试不加权限限制查询
        if (complianceReviewList.isEmpty()) {
            log.warn("使用权限查询无数据，尝试查询所有数据");
            QueryWrapper<ComplianceReview> simpleQuery = new QueryWrapper<>();
            // 只查询非删除状态的数据
            simpleQuery.ne("data_state_code", 0);
            List<ComplianceReview> allData = list(simpleQuery);
            log.info("数据库中总共有 {} 条合规审查数据", allData.size());
            complianceReviewList = allData;
        }

        XSSFWorkbook workbook = null;
        XSSFSheet sheet = null;
        InputStream inputStream = null;
        ByteArrayOutputStream out = null;

        try {
            // 尝试加载模板文件
            ClassPathResource resource = new ClassPathResource("template/contract/complianceReviewLedger.xlsx");
            if (resource.exists()) {
                inputStream = resource.getInputStream();
                workbook = new XSSFWorkbook(inputStream);
                sheet = workbook.getSheetAt(0);
                log.info("使用模板文件创建Excel");
            } else {
                log.warn("模板文件不存在，创建新的Excel文件");
                workbook = new XSSFWorkbook();
                sheet = workbook.createSheet("合规审查台账");
            }

            // 确保表头存在
            if (sheet.getLastRowNum() == 0 || sheet.getRow(0) == null) {
                createHeaderRow(sheet, workbook);
                log.info("创建表头行");
            }

            // 创建单元格样式
            XSSFCellStyle style = createCellStyle(workbook);
            XSSFCellStyle dateStyle = createCellStyle(workbook);
            short dateFormat = workbook.createDataFormat().getFormat("yyyy-MM-dd");
            dateStyle.setDataFormat(dateFormat);
            XSSFCellStyle moneyStyle = createCellStyle(workbook);
            short moneyFormat = workbook.createDataFormat().getFormat("0.00");
            moneyStyle.setDataFormat(moneyFormat);

            // 填充数据
            if (complianceReviewList.isEmpty()) {
                log.warn("没有查询到合规审查数据，将导出空白表格");
            } else {
                log.info("开始填充 {} 条合规审查数据到Excel", complianceReviewList.size());
            }

            for (int i = 0; i < complianceReviewList.size(); i++) {
                ComplianceReview complianceReview = complianceReviewList.get(i);
                XSSFRow row = sheet.createRow(i + 1);

                // 设置行高
                row.setHeightInPoints(35);

                // 填充所有列的数据
                setRiskCellsByContract(row, complianceReview, i + 1, style, dateStyle, moneyStyle);
            }

            // 设置响应头
            String fileName = "合规审查台账.xlsx";
            response.setHeader("Access-Control-Expose-Headers", "Response-Type");
            response.setHeader("Response-Type", "doc");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

            out = new ByteArrayOutputStream();
            OutputStream os = response.getOutputStream();
            workbook.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            log.error("合规审查台账导出异常", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("关闭输出流异常", e);
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流异常", e);
                }
            }
        }
    }

    /**
     * 设置合规审查台账单元格数据
     */
    private void setRiskCellsByContract(XSSFRow row, ComplianceReview complianceReview, int num, XSSFCellStyle style, XSSFCellStyle dateStyle, XSSFCellStyle moneyStyle) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        // 只导出8个字段：序号,事项题目,审查分类,业务领域,送审人,送审单位,送审时间,审查状态
        for (int i = 0; i < 8; i++) {
            XSSFCell cell = row.createCell(i);

            // 日期字段样式
            if (i == 6) { // 送审时间
                cell.setCellStyle(dateStyle);
            } else {
                cell.setCellStyle(style);
            }

            switch (i) {
                case 0: // 序号
                    cell.setCellValue(num);
                    break;
                case 1: // 事项题目
                    cell.setCellValue(complianceReview.getReviewSubject() != null ? complianceReview.getReviewSubject() : "");
                    break;
                case 2: // 审查分类
                    cell.setCellValue(complianceReview.getReviewCategoryName() != null ? complianceReview.getReviewCategoryName() : "");
                    break;
                case 3: // 业务领域
                    cell.setCellValue(complianceReview.getBusinessArea() != null ? complianceReview.getBusinessArea() : "");
                    break;
                case 4: // 送审人
                    cell.setCellValue(complianceReview.getSubmitter() != null ? complianceReview.getSubmitter() : "");
                    break;
                case 5: // 送审单位
                    cell.setCellValue(complianceReview.getSubmittingUnit() != null ? complianceReview.getSubmittingUnit() : "");
                    break;
                case 6: // 送审时间
                    if (complianceReview.getSubmissionDate() != null) {
                        cell.setCellValue(sdf.format(complianceReview.getSubmissionDate()));
                    } else {
                        cell.setCellValue("");
                    }
                    break;
                case 7: // 审查状态
                    cell.setCellValue(complianceReview.getDataState() != null ? complianceReview.getDataState() : "");
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 创建表头行
     */
    private void createHeaderRow(XSSFSheet sheet, XSSFWorkbook workbook) {
        XSSFRow headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(25);

        // 创建表头样式
        XSSFCellStyle headerStyle = workbook.createCellStyle();
        XSSFFont headerFont = workbook.createFont();
        headerFont.setFontName("宋体");
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);

        // 表头内容
        String[] headers = {"序号", "事项题目", "审查分类", "业务领域", "送审人", "送审单位", "送审时间", "审查状态"};

        for (int i = 0; i < headers.length; i++) {
            XSSFCell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);

            // 设置列宽
            if (i == 1) { // 事项题目列宽一些
                sheet.setColumnWidth(i, 8000);
            } else if (i == 6) { // 送审时间
                sheet.setColumnWidth(i, 4000);
            } else {
                sheet.setColumnWidth(i, 3000);
            }
        }
    }

    /**
     * 创建单元格样式
     */
    private XSSFCellStyle createCellStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        style.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        style.setWrapText(true); // 自动换行
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }
}




