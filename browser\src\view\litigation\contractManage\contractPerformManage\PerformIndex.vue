<template>
  <el-container class="container-manage-sg" direction="vertical">
    <el-header>
      <el-card>
        <el-input
            v-model="selectData.fuzzyValue"
            class="filter_input"
            clearable
            placeholder="检索字段（合同名称、合同编号、合同金额、对方签约主体）"
            @clear="reloadContractData"
            @keyup.enter.native="reloadContractData"
        >
          <el-popover slot="prepend" placement="bottom-start" trigger="click" width="1000">
            <el-form label-width="100px" size="mini">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="合同编码">
                    <el-input v-model="selectData.contractCode" clearable placeholder="请输入..."/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="合同名称">
                    <el-input v-model="selectData.contractName" clearable placeholder="请输入..."/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="对方签约主体">
                    <el-input v-model="selectData.otherPartyName" clearable
                              placeholder="请输入..."/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="履行状态">
                    <!--                    <el-input v-model="selectData.performStateCode" clearable placeholder="请输入..." />-->
                    <el-select v-model="selectData.performStateCode" clearable
                               placeholder="请输入.....">
                      <el-option v-for="(item,i) in typeData" :key="item.id" :label="item.dicName"
                                 :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <!--<el-row>
                <el-col :span="12">
                  <el-form-item label="合同金额">
                    <el-input-number v-model="selectData.includingTaxRmbMin" style="width: 45%;float: left;" placeholder="最小值" :controls="false" :precision="2" clearable @focus="utils.inputFocus" />
                    <div style="width: 10%;float: left;text-align: center;" class="label_1">
                      <span>至</span>
                    </div>
                    <el-input-number v-model="selectData.includingTaxRmbMax" style="width: 45%" placeholder="最大值" :controls="false" :precision="2" clearable @focus="utils.inputFocus" />
                  </el-form-item>
                </el-col>
              </el-row>-->
              <el-row>
                <!--<el-col :span="12">
                  <el-form-item label="履行状态">
                    <el-select v-model="selectData.performState" placeholder="请选择..." style="width:100%" clearable @keyup.enter.native="reloadContractData" @clear="reloadContractData">
                      <el-option
                          v-for="item in typeData"
                          :key="item.id"
                          :label="item.dicName"
                          :value="item.dicName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>-->
              </el-row>
              <div style="float: right">
                <el-button icon="el-icon-search" size="mini" type="primary" @click="search_">搜索
                </el-button>
                <el-button icon="el-icon-refresh-left" size="mini" type="primary" @click="empty_">重置
                </el-button>
              </div>
            </el-form>
            <el-button slot="reference" size="mini" type="primary">高级检索</el-button>
          </el-popover>
          <el-button slot="append" icon="el-icon-search" @click="search_"/>
        </el-input>
      </el-card>
    </el-header>
    <el-main>
      <SimpleBoardIndex :hasAdd="true" :body="body" :is-body="true" style="height:40% !important" title="合同列表">
        <template slot='button'>
          <!--          <el-button class="normal-btn" size="mini" @click="handlePerform">管理履行计划</el-button>-->
          <!--          <el-button class="normal-btn" size="mini" @click="handleDate">履行时间轴</el-button>-->
          <el-button class="normal-btn" size="mini" @click="baseWarn">提醒设置</el-button>
          <!--          <el-button class="normal-btn" size="mini" @click="performComplete">履行完成</el-button>-->
          <!--          <el-button class="normal-btn" size="mini" @click="revenueDetail">财务收支明细</el-button>-->
        </template>
        <el-table
            ref="table"
            v-loading="contractLoading"
            :data="contractTableData"
            border
            element-loading-background="rgba(0, 0, 0, 0.5)"
            element-loading-spinner="el-icon-loading"
            element-loading-text="加载中..."
            fit
            height="25vh"
            highlight-current-row
            stripe
            style="width: 100%;"
            @row-click="rowClick"
            @sort-change="tableSort"
        >
          <el-table-column align="center" label="序号" type="index" width="50"></el-table-column>
          <el-table-column label="合同编码" min-width="200" prop="contractCode"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="合同名称" prop="contractName" show-overflow-tooltip
                           width="230"></el-table-column>
          <el-table-column label="合同金额" min-width="100" prop="contractMoney"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="变更后金额" min-width="100" prop="afterChangeMoney"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="已履行金额" min-width="100" prop="fulfillmentAmount"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="待履行金额" min-width="100" prop="contractExecutoryMoney"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="我方签约主体" min-width="300" prop="ourPartyName"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="对方签约主体" min-width="300" prop="otherPartyName"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="收支方向" prop="revenueExpenditure" show-overflow-tooltip
                           width="80"></el-table-column>
          <el-table-column label="履行计划状态" prop="performanceState" show-overflow-tooltip
                           width="80"></el-table-column>
          <el-table-column label="履行进度" min-width="100">
            <template slot-scope="scope">
              <el-progress v-if="getProgress(scope.row)!=100" :percentage="getProgress(scope.row)"
                           :stroke-width="26"
                           :text-inside="true"
                           status="warning"></el-progress>
              <el-progress v-if="getProgress(scope.row)==100" :percentage="getProgress(scope.row)"
                           :stroke-width="26"
                           :text-inside="true"
                           status="success"></el-progress>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="130">
            <template slot-scope="scope">
              <span v-if="scope.row.performState==5">履行完成</span>
              <el-select v-if="scope.row.performState!=5" v-model="scope.row.performState"
                         style="width:100%;height:60%"
                         @change="changePerformState(scope.row)">
                <el-option v-for="item in typeData" :key="item.code" :label="item.dicName"
                           :value="item.id"></el-option>
              </el-select>
            </template>
          </el-table-column>

        </el-table>
      </SimpleBoardIndex>
      <el-footer>
        <pagination
            :limit.sync="selectData.limit"
            :page.sync="selectData.page"
            :total="selectData.total"
            @pagination="reloadContractData"
        />
      </el-footer>
      <SimpleBoardIndex :hasAdd="true" style="height:50% !important" title="履行计划">
        <template slot='button'>
          <span v-if="this.currentRow.id!=='' || this.currentRow.id!=null"
                style="text-align:center;color:red">待计划金额：{{ elsePlanAmount }}，已计划金额：{{
              totalPlanAmount
            }}</span>
          <el-button v-if="this.currentRow.id!=='' && (this.currentRow.performanceStateCode===1 || this.currentRow.performanceStateCode===3)"
                     class="normal-btn"
                     size="mini"
                     @click="addPerformRow">新增履行计划
          </el-button>
          <el-button v-if="this.currentRow.id!=='' && this.currentRow.performanceStateCode!==5"
                     class="normal-btn"
                     size="mini"
                     @click="submitPerform">发布
          </el-button>
          <!--          <el-button class="normal-btn" size="mini" @click="baseWarn">提醒设置</el-button>-->
          <!--          <el-button class="normal-btn" size="mini" @click="performComplete">履行完成</el-button>-->
          <!--          <el-button class="normal-btn" size="mini" @click="revenueDetail">财务收支明细</el-button>-->
        </template>
        <el-table
            ref="table"
            v-loading="performLoading"
            :data="tableData"
            :height="table_height"
            border
            class="indexTable"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            element-loading-spinner="el-icon-loading"
            element-loading-text="加载中..."
            fit
            highlight-current-row
            stripe
            style="width: 100%;">
          <el-table-column align="center" fixed="left" label="序号" type="index" width="50"></el-table-column>
          <el-table-column align="center" label="计划编号" prop="performCode" show-overflow-tooltip
                           width="250"></el-table-column>
          <el-table-column align="center" label="履行事项" prop="performText"
                           show-overflow-tooltip width="200"></el-table-column>
          <el-table-column align="center" label="我方签约主体" prop="ourPartyName" show-overflow-tooltip
                           width="230"></el-table-column>
          <el-table-column align="center" label="对方签约主体" min-width="250" prop="otherPartyName"
                           show-overflow-tooltip
                           show-overflow-tooltip></el-table-column>
          <el-table-column align="center" label="合同条款" prop="contractTerms" show-overflow-tooltip
                           width="150"></el-table-column>
          <el-table-column align="center" label="收支方向" prop="revenueExpenditure"
                           width="150"></el-table-column>
          <el-table-column align="center" label="结算方式" prop="settlementMethod" show-overflow-tooltip
                           width="230"></el-table-column>
          <el-table-column align="center" label="计划履行日期" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.planDate | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="计划履行金额" prop="planAmount"
                           width="100"></el-table-column>
          <el-table-column align="center" label="计划履行比例" width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.planRatio }}%</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="项目编号" prop="projectCode" width="150"></el-table-column>
          <el-table-column align="center" label="币种" prop="currency" width="150"></el-table-column>
          <el-table-column align="center" label="汇率方式" prop="exchangeRateMethod" show-overflow-tooltip
                           width="150"></el-table-column>
          <el-table-column align="center" label="汇率" prop="exchangeRate" width="150"></el-table-column>
          <el-table-column align="center" label="计划履行金额（人民币）" prop="planAmountRmb"
                           width="150"></el-table-column>
          <el-table-column align="center" label="账期天数" prop="periodDay"
                           width="150"></el-table-column>
          <el-table-column align="center" label="计划开票日期" prop="planInvoiceDate" width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.planInvoiceDate | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="履行说明" min-width="300"
                           prop="performExplain"></el-table-column>
          <el-table-column align="center" label="履行进度" prop="performProgress"
                           width="100">
            <template slot-scope="scope">
              <el-progress :percentage="scope.row.performProgress==null?0:scope.row.performProgress"
                           :stroke-width="26"
                           :text-inside="true"
                           :status="scope.row.performProgress===100?'success':'warning'"></el-progress>
            </template>
          </el-table-column>
          <el-table-column align="center" fixed="right" label="操作" width="150">
            <template slot-scope="scope">
              <el-button v-show="scope.row.performState === '1' || scope.row.performState === '3'"
                         type="text"
                         @click="performCopy(scope.row)">复制
              </el-button>
              <el-button v-show="scope.row.performState === '1' || scope.row.performState === '3'"
                         type="text"
                         @click="performEdit(scope.row)">更新
              </el-button>
              <el-button v-show="scope.row.performState === '3' && scope.row.performCode !== null"
                         type="text"
                         @click="performCancel(scope.row)">作废
              </el-button>
              <el-button v-show="(scope.row.performState === '1' || scope.row.performState === '3') && scope.row.performCode === null"
                         type="text"
                         @click="performDelete(scope.row)">删除
              </el-button>
            </template>
          </el-table-column>

        </el-table>
      </SimpleBoardIndex>


      <el-drawer ref="drawer" :title="currentRow.contractName" :visible.sync="performDialog"
                 :wrapper-closable="false"
                 custom-class="case-drawer" size="850px">
        <div class="myCard" style="height: calc(100vh - 120px);">
          <ContractText3
              ref="contText3"
              :approval-id="relationId"
              :approval-money="money"
              :approval-type="moneyWayName"
              :approval-unit="nuit"
              :data="currentRow"
              :data-state="dataState"
              :has-value.sync="this.hasText"
              :money-type="moneyType"
              :myplan-text-data.sync="planTextData"
              @rowClick="performClick"
              @rowEmpty="performEmpty"
              @updateData="updateData"
              @updatePlanData="updatePlanData"
          />

          <ContractText4
              ref="contText4"
              :approval-id="relationId"
              :approval-money="money"
              :approval-type="moneyWayName"
              :data-state="dataState"
              :has-value.sync="this.hasText"
              :myplan-text-data.sync="planTextData1"
              :plan-id="performId"
              :plan-money="performMoney"
              @updateData="updateData"
              @updatePlanData1="updatePlanData1"
          />

        </div>

      </el-drawer>

      <el-drawer ref="drawer" :visible.sync="drawerDialog" :wrapper-closable="false" custom-class="case-drawer"
                 size="600px">
        <div class="slot">
          <div style="font-weight: bolder;font-size: 16px;padding-left: 20px;">
            <i class="el-icon-pie-chart"/>
            <span>履行计划时间轴</span>
          </div>
        </div>
        <div class="myCard" style="height: calc(100vh - 120px);">
          <el-scrollbar style="height:100%">
            <div class="snap-list">
              <el-timeline style="margin-left: 150px;margin-top: 20px">
                <el-timeline-item v-for="(item,index) in tempData.timeAxis"
                                  :key="index"
                                  :class="item.status === true  ?'el-timeline-item-left-6':'el-timeline-item-left-4'"
                                  :color="item.color"
                                  :size="item.status === true ? 'large':'normal'">
                  <div v-if="item.title" class="left-font">{{ item.title }}</div>
                  <div v-if="item.time" class="spaceBetween">{{ item.time }}</div>
                  <div v-if="item.money" class="spaceBetween">{{ item.money }}</div>
                  <div v-if="item.moneyType" class="spaceBetween">{{ item.moneyType }}</div>
                  <!--<div v-if="item.chargePerson" class="spaceBetween">{{ item.chargePerson}}</div>
                  <div v-if="item.responsiblePerson" class="spaceBetween">{{ item.responsiblePerson}}</div>-->
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-scrollbar>
        </div>
      </el-drawer>

      <el-dialog :close-on-click-modal="false" :visible.sync="warnDialog" append-to-body title="提醒设置"
                 width="70%">
        <el-table
            v-draggable
            :data="warnData"
            :max-height="table_height"
            border
            fit
            highlight-current-row
            size="mini"
            stripe
        >
          <el-table-column align="center" label="序号" type="index"/>
          <el-table-column align="center" label="提醒类型" min-width="40" prop="warnType"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <span class="viewSpan">{{ scope.row.warnType }}</span>
            </template>
          </el-table-column>
          >
          <el-table-column align="center" label="是否提醒" min-width="20" prop="warn" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-switch
                  v-model="scope.row.warn"
                  active-color="#13ce66"
                  inactive-color="#DCDFE6"
              >
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column align="center" label="提醒人员" min-width="60" prop="warnPsnName"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-input
                  v-model.trim="scope.row.warnPsnName"
                  disabled="disabled"
                  show-word-limit
                  style="width:70%"
              />
              <el-button v-if="dataState!=='view'" size="mini" @click="orgEdit(scope.$index, scope.row)">
                添加人员
              </el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="提醒时间" min-width="40" prop="warnTimeName"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-select
                  v-model="scope.row.warnTimeName"
                  multiple
                  placeholder="请选择"
                  style="width: 100%"
                  @change="changeTime"
              >
                <el-option
                    v-for="item in timeData"
                    :key="item.id"
                    :label="item.dicName"
                    :value="item.dicName"
                />
              </el-select>
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button class="negative-btn" icon="" @click="cancel_">取消</el-button>
          <el-button
              v-if="dataState!=='view'"
              class="active-btn"
              icon=""
              type="primary"
              @click="sure_"
          >确定
          </el-button>
        </span>
      </el-dialog>

      <el-dialog :close-on-click-modal="false" :visible.sync="revenueDetailDialog" append-to-body
                 title="财务收支明细"
                 width="85%">
        <el-table
            v-draggable
            :data="revenueDetailData"
            :max-height="table_height"
            border
            fit
            highlight-current-row
            size="mini"
            stripe
        >
          <el-table-column align="center" label="序号" type="index"/>
          <el-table-column label="履行日期" prop="paetDate" show-overflow-tooltip width="80"/>
          <el-table-column label="收支方向" prop="collPaetBilType" show-overflow-tooltip width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.collPaetBilType === '11' ? '收入' : '支出' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="履行金额" prop="actPaetAmo" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.actPaetAmo | toThousandFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column label="币种" prop="currCd" show-overflow-tooltip width="60"/>
          <el-table-column label="汇率" prop="paetAplyExra" show-overflow-tooltip width="60"/>
          <el-table-column label="结算方式" prop="fundSettWay" show-overflow-tooltip/>
          <el-table-column label="项目编号" prop="projNum" show-overflow-tooltip/>
          <el-table-column label="我方单位" prop="paetCoOrgCod" show-overflow-tooltip/>
          <el-table-column label="对方单位" prop="contCoCod" show-overflow-tooltip/>
          <el-table-column label="资金单号" prop="settBilNum" show-overflow-tooltip/>

        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button class="negative-btn" icon="" @click="revenueDetailCancel_">取消</el-button>
          <el-button
              v-if="dataState!=='view'"
              class="active-btn"
              icon=""
              type="primary"
              @click="revenueDetailSure_"
          >确定
          </el-button>
        </span>
      </el-dialog>


      <el-dialog
          :close-on-click-modal="false"
          :visible.sync="orgVisible"
          append-to-body
          title="选择人员"
          width="50%"
      >
        <div class="el-dialog-div">
          <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="is_Check"
                   :is-checked-user="isCheckedUser"
                   :is-filter="true" :is-not-cascade="true" :show-user="showUser"/>
        </div>
        <span slot="footer" class="dialog-footer">
        <el-button class="negative-btn" icon="" @click="cancelDept_">取消</el-button>
        <el-button class="active-btn" type="primary" @click="choiceDeptSure_">确定</el-button>
          </span>
      </el-dialog>

      <contract-perform-edit-dialog :rowData="performRowData" :show="performShow"
                                    v-on:listenToChildEvent="closePerFormDialog"></contract-perform-edit-dialog>
    </el-main>

  </el-container>
</template>

<script>
import {mapGetters} from "vuex"
import perResultApi from '@/api/contract/contractPerformResult'
import contractApi from '@/api/contract/contract'
import noticeApi from '@/api/_system/notice'
import middleTableApi from "@/api/middleTable/middleTable";
import bmContractApi from '@/api/contract/bmContract';
import bmContractPerformApi from '@/api/contract/bmContractPerform';

import pagination from '../../../components/Pagination/PaginationIndex'
import SimpleBoardIndex from "@/view/components/SimpleBoard/SimpleBoardIndex"
import orgTree from '@/view/components/OrgTree/OrgTree'
import ContractText3 from './contractText3'
import ContractText4 from './contractText4'
import contractPerformEditDialog from './child/contract-perform-edit-dialog'
import SimpleBoard from "@/view/components/SimpleBoard/SimpleBoardViewCase.vue";

export default {
  name: 'PerformIndex',
  inject: ['layout'],
  computed: {
    ...mapGetters(['orgContext', 'currentFunctionId']),
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    }
  },
  components: {
    SimpleBoard,
    pagination, SimpleBoardIndex, orgTree, ContractText3, ContractText4, contractPerformEditDialog},
  filters: {
    contractMoneyFilter(row) {
      if ("合同变更" === row.dataTypeName) {
        return row.changeMoney
      } else {
        return row.includingTaxRmb
      }
    }
  },
  watch: {
    /*contractTableData:function(){
        this.$nextTick(function () {
            this.$refs.table.setCurrentRow(this.contractTableData[this.index])

        })
    }*/
  },
  data() {
    return {
      contractLoading: false,
      performLoading: false,
      elsePlanAmount: 0,//剩余计划金额
      totalPlanAmount: 0,//计划总金额
      body: '当前列表仅展示已生效合同数据；如有未显示数据请检查合同是否完成生效操作。',
      performRowData: {},
      performShow: false,
      tempData: {
        tags: [],
        timeAxis: []
      },
      table: null,
      warnData: [
        {
          id: this.utils.createUUID(),
          warnType: '合同到期提醒',
          warn: false,
          warnPsnName: null,
          warnPsnId: null,
          warnTime: null,
          relationId: null,
          warnTimeName: null
        },
        {
          id: this.utils.createUUID(),
          warnType: '合同计划到期提醒',
          warn: false,
          warnPsnName: null,
          warnPsnId: null,
          warnTime: null,
          relationId: null,
          warnTimeName: null
        }
      ],
      revenueDetailData: [],
      tableTempData: [],
      table_height: null, // 定义表格高度
      tableData: [], // 定义表格数据源
      contractTableData: [], // 合同列表
      warnDialog: false,
      revenueDetailDialog: false,
      showUser: false,
      is_Check: false,
      isCheckedUser: false,
      orgVisible: false,
      zxcheckedData: [],
      warnIndex: 0,
      index: 0,
      planTextData1: [],
      planTextData: [],
      hasText: true,
      drawerDialog: false,
      performDialog: false,
      isShow: false,
      dataState: "edit",
      isResouceShow: 0,
      moneyWayName: null,
      nuit: null,
      relationId: null,
      money: null,
      moneyType: null,
      planId: null,
      chart: null,
      performId: null,
      performMoney: '0',
      performEndTime: null, // 履行结束时间
      startTime: null, // 履行开始时间
      state: null,
      isFirst: true,
      tableData1: null, // 履行数据源
      timeData: [{"id": "0", "dicName": "当天（多选）"}, {"id": "7", "dicName": "提前7天"}, {
        "id": "30",
        "dicName": "提前30天"
      }, {"id": "90", "dicName": "提前90天"}, {"id": "180", "dicName": "提前180天"}],
      typeData: [
        {"id": "3", "dicName": "履行中", "color": "#45b97c"},
        {"id": "4", "dicName": "异常履行", "color": "#f47920"},
        {"id": "5", "dicName": "履行完成", "color": "#45b97c"},
      ],
      selectData: {
        takeEffectCode: '3',
        performFlag: '1',
        page: 1,
        limit: 10,
        total: 0,
        functionType: "perform"

      },
      perform: {
        id: null,
        contractName: null, // 合同名称
        performName: null, // 履行标题
        performMain: null, // 履行主体
        performMainId: null, // 履行主体id
        performTask: null, // 履行任务
        performRemark: null, // 履行说明
        performStartTime: null, // 开始时间
        performEndTime: null, // 结束时间
        performStatus: null, // 合同履行状态
        isPostpone: null, // 是否逾期
        relationId: null, // 关联合同id
        createOgnName: null, //  经办单位
        createDeptName: null, // 经办部门
        createPsnName: null, // 经办人
        createTime: null, // 经办时间
        createOgnId: null,
        createDeptId: null,
        createPsnId: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
        page: 1,
        limit: 10,
        total: 0,
        sortName: null,
        order: null
      },
      contractId: null,
      currentRow: {}
    }
  },
  activated() {
    this.reloadContractData()
  },
  created() {
    //      this.refreshData()

    this.reloadContractData();
    if (this.$route.query.read === 'false') {
      noticeApi.read({sid: this.$route.query.sid})
    }
  },
  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 20
      const self = this
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 20
      }
    })
  },
  methods: {
    //加载已生效的合同数据
    reloadContractData() {
      this.selectData.orgId = this.orgContext.currentOrgId
      this.selectData.functionType = "perform"
      this.contractLoading = true;
      bmContractApi.query(this.selectData).then(res => {
        this.contractTableData = res.data.data.records
        if (this.contractTableData.length > 0) {
          this.currentRow = this.contractTableData[0];
          this.loaddingPerformData(this.currentRow.id, this.currentRow);
        }
        this.selectData.total = res.data.data.total
        this.contractLoading = false;
      })
    },
    contractMoneyFormat(row, column, cellValue, index) {
      if (row.moneyTypeName === '总价') {
        return row.contractMoney
      } else {
        return row.contractMoneyString
      }
    },
    tableSort(column, prop, order) {
      this.selectData.sortName = column.prop
      this.selectData.order = column.order === "ascending"
      this.reloadContractData()
    },
    search_() { // 查询
      this.reloadContractData()
    },
    empty_() {
      this.selectData = {
        contractName: '', // 合同名称
        includingTaxRmbMin: null,
        includingTaxRmbMax: null,
        otherPartyName: null, // 对方签约主体
        performState: null,
        fuzzyValue: null,
        page: 1,
        limit: 10,
        total: 0,
        sortName: null,
        order: false,
        orgId: this.orgContext.currentOgnId,
        takeEffectCode: '3',
        performFlag: '1'
      }
      this.reloadContractData()
    },
    refresh_() {
      this.empty();
      //                this.reloadContractData()
    },
    /*refreshData() { //  查询方法
        this.selectData.orgId = this.orgContext.currentOrgId
        this.selectData.dataState = "已完成"
        this.selectData.perform=true
        contractApi.queryContractPerform(this.selectData).then(res => {
            this.tableData = res.data.data.records
            this.selectData.total = res.data.data.total
            if(this.tableData.length>0){
                this.rowClick(this.tableData[0])
            }
        })
    },*/
    rowClick(row) {
      this.currentRow = row;
      this.loaddingPerformData(row.id, row);
    },
    performClick(row) {
      this.performId = row.id
      this.performMoney = row.performMoney;
      this.relationId = row.relationId
      perResultApi.queryPerformResult({relationId: this.relationId, planId: this.performId}).then(res => {
        this.planTextData1 = res.data.data

      })
    },
    performEmpty() {
      this.rowClick(this.currentRow)
      // this.planTextData1=[]
    },
    searchTypeChange() {
      this.selectData.contractTypeName = ''
      const names = this.$refs.cascadersAddr.getCheckedNodes()[0].pathLabels
      for (let i = 0; i < names.length; i++) {
        this.selectData.contractTypeName += names[i] + '/'
      }
      //  this.selectData.contractNature = names[0]
      // this.selectData.contractTypeName = this.selectData.contractTypeName.substring(this.selectData.contractTypeName.indexOf('/') + 1)
      this.selectData.contractTypeName = this.selectData.contractTypeName.substring(0, this.selectData.contractTypeName.length - 1)
    },
    SectionToChinese(section) {
      var chnNumChar = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      var chnUnitChar = ['', '十', '百', '千', '万', '亿', '万亿', '亿亿']
      var strIns = '';
      var chnStr = ''
      var unitPos = 0
      var zero = true
      while (section > 0) {
        var v = section % 10
        if (v === 0) {
          if (!zero) {
            zero = true
            chnStr = chnNumChar[v] + chnStr
          }
        } else {
          zero = false
          strIns = chnNumChar[v]
          strIns += chnUnitChar[unitPos]
          chnStr = strIns + chnStr
        }
        unitPos++
        section = Math.floor(section / 10)
      }
      return chnStr
    },
    add_() {
      this.dataState = 'add'
      // 此处应判断 结束时间 和 状态
      if (this.perform.contractName === null) {
        this.$message.warning('请选择合同信息!')
        return false
      }
      if (this.state === '正常履行完成' || this.state === '逾期履行完成') {
        this.$message.warning('该履行已经完成,无法新增!')
        return false
      }
      if (this.startTime === null) {
        this.$message.warning('请填写履行结束时间!')
        return false
      }
      // this.startTime = this.tableData1[this.tableData1.length - 1].performEndTime
      this.perform.id = this.utils.createUUID()
      this.perform.performName = '第' + this.SectionToChinese(this.perform.total + 1) + '次履行'
      this.perform.performStartTime = this.startTime
      this.perform.performMain = null
      this.perform.performMainId = null
      this.perform.performTask = null
      this.perform.performRemark = null
      this.perform.performEndTime = null
      this.perform.isPostpone = null
      this.perform.createTime = new Date()
      this.isShow = true
      this.$nextTick(function () {
        this.$refs['dataForm'].clearValidate()
      })
    },
    performPlan() {
      if (this.relationId != '' && this.relationId != null) {
        this.$router.push({name: 'performPlan', params: {dataId: this.relationId, dataState: 'add'}})
      } else {
        this.$message.warning('请选中一条数据!')
      }
    },
    performResult() {
      if (this.relationId != '' && this.relationId != null) {
        this.$router.push({name: 'performResult', params: {dataId: this.relationId, dataState: 'add'}})
      } else {
        this.$message.warning('请选中一条数据!')
      }
    },
    performRecord() {

    },
    resize() {
      console.log('resize')
    },
    changeStatus($event, selectedRef) {
      const color = this.getColor($event)
      // 改变下拉框颜色值
      this.$refs[selectedRef].$el.children[0].children[0].style.color = '' + color + ''
      contractApi.updateContractPerformState({id: selectedRef, stateName: $event}).then(response => {
        this.updateData(selectedRef);
      })
    },
    loadtatus($event, selectedRef) {
      const color = this.getColor($event)
      // 改变下拉框颜色值
      this.$refs[selectedRef].$el.children[0].children[0].style.color = '' + color + ''
    },
    getSytle(dicName) {
      if (dicName !== null) {
        for (let i = 0; i, this.typeData.length; i++) {
          if (this.typeData[i].dicName == dicName) {
            return "color:" + this.typeData[i].color
          }
        }
      }
      return "color:#82848a"
    },
    getColor(dicName) {
      if (dicName !== null) {
        for (let i = 0; i, this.typeData.length; i++) {
          if (this.typeData[i].dicName == dicName) {
            return this.typeData[i].color
          }
        }
      }
      return "#82848a"
    },
    updatePlanData(val) {
      this.planTextData = val
    },
    updateData(val) {
      contractApi.queryContractApprovalById({id: val}).then(response => {
        var row = response.data.data.contractApproval
        this.index = this.tableData.findIndex(item => item.id === val)
        this.$set(this.tableData, this.index, row)
        this.rowClick(row);
      })

    },
    updatePlanData1(val) {
      //this.rowClick(this.currentRow)
      this.planTextData1 = val
    },
    // 格式化日期
    dateFormat(cellValue) {
      if (cellValue != null && cellValue !== '') {
        var currentDate = new Date(cellValue)
        var year = currentDate.getFullYear()
        var month = (currentDate.getMonth() + 1) + ''
        var day = (currentDate.getDate()) + ''
        if (month.length === 1) {
          month = '0' + month
        }
        if (day.length === 1) {
          day = '0' + day
        }
        var formatDate1 = year + '-' + month + '-' + day
        return formatDate1
      }
    },
    // 查看时间轴
    handleDate() {
      if (this.currentRow === undefined || this.currentRow === null || this.tableData.length === 0) {
        this.$message.error('请选择查看的数据！')
        return
      }
      var row = this.currentRow
      var showTemp = {
        title: null,
        time: null,
        money: null,
        ratio: null,
        chargePerson: null,
        responsiblePerson: null,
        moneyType: null,
        status: null,
        color: null
      }
      var tempArr = []
      var tempArrTotal = []

      this.drawerDialog = true
      this.tempData.timeAxis = []
      contractApi.queryContractApprovalById({id: row.id}).then(response => {
        var performData = response.data.data.planTextData
        var chargePerson = response.data.data.contractApproval.chargePerson;  //履行责任人
        var responsiblePerson = response.data.data.contractApproval.responsiblePerson;  //履行经办人
        var performResult = [];
        performData.forEach(perform => {
          showTemp = {
            title: null,
            time: null,
            money: null,
            ratio: null,
            chargePerson: null,
            responsiblePerson: null,
            moneyType: null,
            status: null,
            color: null
          }
          tempArr = []
          showTemp.title = "履行计划：" + perform.performName
          showTemp.time = "履行时间：" + perform.performEndTime !== null ? this.dateFormat(perform.performEndTime) : ''
          showTemp.money = "履行金额：" + perform.performMoney !== null ? perform.performMoney : ''
          showTemp.ratio = "履行比例：" + perform.ratio !== null ? perform.ratio : ''
          showTemp.chargePerson = "履行责任人：" + row.createPsnName
          showTemp.responsiblePerson = "履行经办人：" + row.createPsnName
          showTemp.moneyType = "收支方向：" + perform.performTypeName !== null ? perform.performTypeName : ''
          showTemp.color = '#82848a'
          showTemp.status = true
          performResult = perform.contractPerformResult
          var performDate = new Date(perform.performEndTime)
          performDate.setDate(performDate.getDate() + 1);
          if (performResult.length < 1 && performDate <= new Date()) {
            showTemp.color = '#ff4949'
          }
          for (let i = 0; i < performResult.length; i++) {
            showTemp.color = "#11b95c"
            if (performDate <= new Date(performResult[i].performEndTime)) {
              showTemp.color = "#ff4949"
              break
            }
          }
          tempArr.push(Object.assign({}, showTemp))  //履行计划
          tempArrTotal = tempArrTotal.concat(tempArr)
          performResult.forEach(item => {
            showTemp = {
              title: null,
              time: null,
              money: null,
              ratio: null,
              chargePerson: null,
              responsiblePerson: null,
              moneyType: null,
              status: null,
              color: "#11b95c"
            }
            tempArr = []
            showTemp.title = "履行结果：" + item.performName
            showTemp.time = "实际履行时间：" + this.dateFormat(item.performEndTime)
            showTemp.money = "实际履行金额：" + item.moneyDirection
            if (performDate <= new Date(item.performEndTime)) {
              showTemp.color = "#ff4949"
            }
            showTemp.status = false
            tempArr.push(Object.assign({}, showTemp))
            tempArrTotal = tempArrTotal.concat(tempArr)
          })
        })
        this.tempData.timeAxis = tempArrTotal
      })
    },
    getClass(row) {
      //判断是否一个月未进行数据修改
      var starDate = row.row.updateTime;
      var date = new Date();
      var sDate = new Date(starDate).getTime();
      var eDate = date.getTime();
      //将当前月份加1，下移到下一个月
      date.setMonth(date.getMonth() + 1);
      //将当前的日期置为0，
      date.setDate(0);
      var thisMothDays = 1000 * 3600 * 24 * date.getDate();
      if ((eDate - sDate > thisMothDays) && row.row.performState !== "逾期履行完成" && row.row.performState !== "正常履行完成" && row.row.performState !== "合同关闭") {
        return {"background-color": "#ff494954"}
      } else {
        return ""
      }
    },
    baseWarn() {
      if (this.currentRow === undefined || this.currentRow === null) {
        this.$message.error('请选择查看的数据！')
        return
      }
      this.warnData = [
        {
          id: this.utils.createUUID(),
          warnType: '合同计划到期提醒',
          warn: false,
          warnPsnName: null,
          warnPsnId: null,
          warnTime: null,
          relationId: this.currentRow.id,
          warnTimeName: null,
          createOrgId: this.orgContext.currentOrgId
        }
      ]
      console.log(this.warnData);
      this.warnDialog = true
      // contractWarnApi.queryAll({ relationId: this.relationId}).then(res => {
      //   if(res.data.data.length!=0){
      //     this.warnData = res.data.data
      //     this.warnData[0].warnTimeName = JSON.parse(this.warnData[0].warnTimeName)
      //     this.warnData[1].warnTimeName = JSON.parse(this.warnData[1].warnTimeName)
      //   }else{
      //     this.warnData =[
      //       { id: this.utils.createUUID(), warnType: '合同到期提醒', warn: false, warnPsnName: null, warnPsnId: null, warnTime: null, relationId: null, warnTimeName: null },
      //       { id: this.utils.createUUID(), warnType: '合同计划到期提醒', warn: false, warnPsnName: null, warnPsnId: null, warnTime: null, relationId: null, warnTimeName: null }
      //     ]
      //   }
      //   this.warnDialog = true
      // })
    },
    performComplete() {
      if (this.currentRow === undefined || this.currentRow === null || this.tableData.length === 0) {
        this.$message.error('请选择查看的数据！')
        return
      }
      var performState = '履行完成';
      if (this.currentRow != null && this.currentRow != '') {
        if (this.currentRow.performState == performState) {
          this.$message.warning('此条数据已履行完成!')
          return;
        }

        this.$confirm('是否确定合同履行完成?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 发送履行完成
          contractApi.updateContractPerformState({id: this.relationId, stateName: performState}).then(response => {
            this.updateData(this.relationId);
            // contractEvaluateApi.saveByPerformComplete(this.currentRow).then(responseData => {
            //   console.log("保存成功")
            // });
            this.$message.success('履行完成!');
          })
        }).catch(() => {
        });
      } else {
        this.$message.warning('请选中一条数据!')
      }
    },
    revenueDetail() {
      if (this.currentRow === undefined || this.currentRow === null || this.tableData.length === 0) {
        this.$message.error('请选择查看的数据！')
        return
      }
      this.revenueDetailData = []
      let contractId = this.tableTempData.id
      middleTableApi.queryByContractId({id: contractId}).then(res => {
        this.revenueDetailData = res.data.data
      })
      this.revenueDetailDialog = true
    },
    cancel_() {
      this.warnDialog = false
    },
    revenueDetailCancel_() {
      this.revenueDetailDialog = false
    },
    sure_() {
      //this.warnData[0].relationId = this.relationId
      bmContractPerformApi.createScheduler(this.warnData[0]).then(res => {
        if (res.data.data) {
          this.$message.success("提醒成功！");
        } else {
          this.$message.error("提醒失败！");
        }
        this.warnDialog = false
      });


    },
    revenueDetailSure_() {
      this.revenueDetailDialog = false
    },
    orgEdit(index, row) {
      /* if("合同到期提醒" === row.warnType){
         this.warnIndex = 0
       }else{
         this.warnIndex = 1
       }*/
      this.warnIndex = index
      this.isCheckedUser = true
      this.showUser = true
      this.orgVisible = true
      this.is_Check = true
    },
    cancelDept_() {
      this.orgVisible = false
    },
    choiceDeptSure_() {
      if (this.zxcheckedData) {
        for (let i = 0; i < this.zxcheckedData.length; i++) {
          const item = this.zxcheckedData[i];
          if (i === 0) {
            this.warnData[this.warnIndex].warnPsnName = item.name
            this.warnData[this.warnIndex].warnPsnId = item.unitId
          } else {
            this.warnData[this.warnIndex].warnPsnName += '、' + item.name
            this.warnData[this.warnIndex].warnPsnId += ',' + item.unitId
          }
        }
        this.orgVisible = false
      } else {
        this.warnData[this.warnIndex].warnPsnId = null
        this.warnData[this.warnIndex].warnPsnName = null
        this.orgVisible = false
      }
    },
    handlePerform() {
      if (this.currentRow === undefined || this.currentRow === null || this.tableData.length === 0) {
        this.$message.error('请选择查看的数据！')
        return
      }
      this.performDialog = true
      this.updateContractPerformResult();
    },
    changeTime(val) {
      // this.warnData[this.warnIndex].warnTimeName = val
      this.warnData[this.warnIndex].warnTime = val
      /*this.warnData[this.warnIndex].warnTimeName = ""
      var c = ''
      if(!val){
        val.forEach((item, index) => {
            c = c + ',' + item.dicName
        })
      }
      this.warnData[this.warnIndex].warnTimeName = c*/
    },
    updateContractPerformResult() {
      middleTableApi.updateContractPerformResult({relationId: this.tableTempData.id}).then(() => {
        perResultApi.queryPerformResult({relationId: this.relationId, planId: this.performId}).then(res => {
          this.planTextData1 = res.data.data
        })
      })
    },
    //添加计划弹窗
    addPerformRow() {
      //               alert(222);
      this.currentRow.type = 'add';
      this.currentRow.elsePlanAmount = this.elsePlanAmount;
      if (this.currentRow === undefined || this.currentRow === null || this.currentRow.id === undefined) {
        this.$message({
          showClose: true,
          message: '请选择合同信息！',
          type: 'warning',
        });

      } else {
        this.performShow = true;
        this.performRowData = this.currentRow;
      }
    },
    closePerFormDialog(flag, contractId) {
      this.performShow = false;
      let obj = this.contractTableData.find(contract => {
        return contract.id == contractId;
      })
      this.loaddingPerformData(obj.id, obj);
      this.performRowData = {};
      //      this.currentRow = {};
    },
    loaddingPerformData(contractId, row) {
      this.performLoading = true;
      bmContractPerformApi.queryByContractId({contractId: contractId}).then(res => {
        this.tableData = res.data.data;
        let totPlamount = 0;
        let totalAmount = (row.afterChangeMoney == '' || row.afterChangeMoney == null) ? row.contractMoney : row.afterChangeMoney;
        console.log(totalAmount);
        this.tableData.forEach(item => {
          if (item.planAmount != null && item.planAmount !== '' && item.performState !== '6') {
            totPlamount += item.planAmount;
          }
        })
        this.totalPlanAmount = parseFloat(totPlamount).toFixed(2);
        let contractSuppleExecutedMoney = row.contractSuppleExecutedMoney == null ? 0 : row.contractSuppleExecutedMoney;
        this.elsePlanAmount = parseFloat((totalAmount - contractSuppleExecutedMoney - totPlamount).toFixed(2));
        this.performLoading = false;
      });

    },
    performCopy(row) {
      this.performRowData = row;
      this.performRowData.type = 'copy';
      this.performRowData.elsePlanAmount = this.elsePlanAmount;
      this.performRowData.performCode = null
      this.performShow = true;
    },
    performEdit(row) {
      row.type = 'edit';
      this.performShow = true;
      let editTotUseMoney = this.elsePlanAmount + row.planAmount;
      row.elsePlanAmount = editTotUseMoney;
      this.performRowData = row;
    },
    performCancel(row) {
      if(row.planRatio >=1){
        this.$message.error('计划履行比例不为 0% 时，不能进行作废');
        return
      }
      this.$confirm('作废后代表该履行计划中的事项不再执行，财务相关系统不再允许关联该履行计划。', '是否确认作废？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        bmContractPerformApi.cancelContractPerform({id: row.performId}).then(res => {
          if(res.data.data === true){
            this.$message.success('作废成功');
          }else{
            this.$message.error('作废失败');
          }
          this.loaddingPerformData(row.contractId, this.currentRow);
        });
      }).catch(action => {
      })
    },
    performDelete(row) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        bmContractPerformApi.deleteContractPerform({id: row.performId}).then(() => {
          this.$message.success('删除成功');
          this.loaddingPerformData(row.contractId, this.currentRow);
        });
      }).catch(action => {
      })
    },
    //提交计划
    submitPerform() {
      if (this.currentRow === undefined || this.currentRow === null || this.currentRow.id === undefined) {
        this.$message({
          showClose: true,
          message: '请选择合同信息！',
          type: 'warning',
        });

      } else {
        let flag = this.checkPerform();
        if (flag) {
          this.$confirm('是否确认提交计划？提交后无法修改履行计划', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            bmContractPerformApi.submitPerform({contractId: this.currentRow.id}).then(() => {
              this.$message.success('提交成功');
              this.reloadContractData();
              //this.loaddingPerformData(this.currentRow.id,this.currentRow);
            })
          }).catch(action => {
          })
        }
      }
    },
    //校验计划信息
    checkPerform() {
      let flag = true;
      let totalMoney = 0;
      let planMoneyTotal = 0;
      if (this.currentRow.afterChangeMoney !== '' && this.currentRow.afterChangeMoney != null) {
        totalMoney = this.currentRow.afterChangeMoney;
      } else {
        totalMoney = this.currentRow.contractMoney;
      }
      //遍历所有的履行计划，校验相关必填字段，及计划履行金额
      this.tableData.map(item => {
        //累加所有计划履行金额
        if (item.planAmount > 0) {
          planMoneyTotal += item.planAmount;
        }
      })
      let contractSuppleExecutedMoney = this.currentRow.contractSuppleExecutedMoney == null ? 0 : this.currentRow.contractSuppleExecutedMoney;
      planMoneyTotal = parseFloat(parseFloat(planMoneyTotal).toFixed(2))
      let totalPlanAmount = parseFloat(parseFloat((totalMoney - contractSuppleExecutedMoney)).toFixed(2))
      if (totalPlanAmount!== planMoneyTotal) {
        flag = false;
        this.$message.warning('计划总金额与合同金额不一致');
      }
      return flag;
    },
    getProgress(row) {
      //                debugger;
      let totalAmount = row.contractMoney;
      if (row.afterChangeMoney != '' && row.afterChangeMoney != null) {
        totalAmount = row.afterChangeMoney;
      }
      if (row.fulfillmentAmount == null || row.fulfillmentAmount == '') {
        return 0;
      } else {
        let num = row.fulfillmentAmount / totalAmount * 100
        return num.toFixed(0);
      }
    },
    changePerformState(row) {
      let progress = this.getProgress(row);
      if (row.performState == '5') {
        if (progress == '100.00' || this.moneyType !== '固定价款') {
          this.$confirm('确认变更合同履行状态为履行完毕？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let obj = {id: row.id, contractCode: row.contractCode, state: 5};
            bmContractApi.changeContractPerformState(obj).then(res => {
              if (res.status == 200) {
                this.$message.success('变更成功');
                this.reloadContractData();
              } else {
                this.$message.error('变更失败,有履行计划或履行记录未完成！');
                this.reloadContractData();
              }
            });
          });
        } else {
          this.$message.warning('合同未全部履行，无法变更状态');
          this.reloadContractData();
        }
      } else {
        let title = row.performState == '3' ? "履行中" : "异常履行";
        this.$confirm('确认变更合同履行状态为' + title + '？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let obj = {id: row.id, contractCode: row.contractCode, state: row.performState};
          bmContractApi.changeContractPerformState(obj).then(res => {
            if (res.status == 200) {
              this.$message.success('变更成功');
              this.reloadContractData();
            } else {
              this.$message.error('变更失败！');
            }
            this.reloadContractData();
          });
        });

      }
    }

  }
}
</script>
<style scoped>
.snap-list .el-timeline {
  padding-left: 130px;
  width: 70%;
}

/deep/ .left-font {
  font-size: 16px;
  color: #1890FF;
  font-weight: bold
}

/deep/ .el-timeline-item-left-6 .el-timeline-item__wrapper {
  left: -260px;

}

/deep/ .el-timeline-item-left-4 .el-timeline-item__wrapper {
  left: 2px;
}

/deep/ .el-timeline-item__node--large {
  /* border:solid 3px #dfe4ed;*/
  left: -4px;
  width: 17px;
  height: 17px;
}

.case-drawer {
  width: 800px !important;
}

.case-drawer .el-drawer__header {
  margin-bottom: 0;
}

.case-drawer .el-scrollbar__wrap {
  overflow-x: hidden;
}

.spaceBetween {
  color: #82848a;
  padding-top: 4px;
}


::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: #e4e4e4;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: #a1a3a9;
  border-radius: 6px;
}

/deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}


.filter_input .el-input-group__prepend {
  color: #FFFFFF;
  background-color: #1890ff;
  border-color: #1890ff;
}


input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.myNew .el-input__inner {
  background: #fff;
  height: 32px;
  border: 1px solid;
  border-color: #3E7BFA;
  line-height: 32px;
  color: #3E7BFA
}

.myNew .el-input__suffix {
  color: #3E7BFA;
}

.myNew input::-webkit-input-placeholder {
  color: #3E7BFA;
}


.step {
  position: relative;
  display: flex;
  font-size: 0;
}

.left {
  flex-grow: 0;
  position: relative;
  display: inline-block;
  text-align: center;
  font-weight: bold;
  width: 70%;
  font-size: 12px;
  line-height: 20px;
  height: 20px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  border-width: 1px;
  border-style: solid;
}

.left1 {
  flex-grow: 0;
  position: relative;
  display: inline-block;
  color: #333;
  text-align: center;
  font-weight: bold;
  width: 70%;
  font-size: 12px;
  line-height: 20px;
  height: 20px;
  border-radius: 5px;
  border-width: 1px;
  border-style: solid;
}

.right1 {
  flex-grow: 1;
  position: relative;
  display: inline-block;
  /* width:30%; */
  background: #fff;
  color: #333;
  font-weight: bold;
  text-align: center;
  font-size: 12px;
  line-height: 20px;
  height: 20px;
  text-align: center;
  border-radius: 5px;
  border-width: 1px;
  border-style: solid;
}

.right {
  flex-grow: 1;
  position: relative;
  display: inline-block;
  /* width:30%; */
  background: #fff;
  color: #333;
  font-weight: bold;
  text-align: center;
  font-size: 12px;
  line-height: 20px;
  height: 20px;
  text-align: center;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  border-width: 1px;
  border-style: solid;
}

.tip-arrow {
  position: absolute;
  font-size: 11px;
  left: 38%;
  display: inline-block;
  width: 7px;
  height: 7px;
  z-index: 10;
}

.bar-tip-box {
  position: absolute;
  top: -5px;
  right: 50%;
  transform: translate(50%, -100%);
  text-align: left;
  padding: 5px 10px;
  width: max-content;
  color: #fff;
  font-size: 12px;
  font-weight: 400;
  border-radius: 3px;
  background-color: #fff;
  z-index: 10;
}


.bar-tip-box p {
  margin: 0;
  padding-bottom: 5px;
}

.el-table__body .el-table__row {
  background-color: transparent;
}
</style>
