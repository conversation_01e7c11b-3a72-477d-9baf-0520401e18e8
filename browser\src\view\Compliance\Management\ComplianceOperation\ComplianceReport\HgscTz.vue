<template>
  <el-container class="container-manage-sg" direction="vertical">
    <el-header>
      <el-card>
        <div>
          <el-input v-model="tableQuery.fuzzyValue" class="filter_input" clearable placeholder="检索字段（事项题目、审查分类、送审部门）"
            @clear="refreshData" @keyup.enter.native="refreshData">
            <el-popover slot="prepend" placement="bottom-start" trigger="click" width="1000">
              <el-form ref="queryForm" label-width="100px" size="mini">
                <!-- 选择部门弹窗 -->
                <el-dialog :close-on-click-modal="false" :visible.sync="deptOrgVisible" title="选择部门" width="50%">
                  <div class="el-dialog-div" style="z-index: 999999999">
                    <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="true"
                      :is-checked-user="false" :is-filter="true" :is-not-cascade="true" :show-user="false" />
                  </div>
                  <span slot="footer" class="dialog-footer">
                    <el-button class="negative-btn" icon="" @click="deptOrgCancel">取消</el-button>
                    <el-button class="active-btn" icon="" type="primary" @click="choiceNoticeDeptSure">确定</el-button>
                  </span>
                </el-dialog>
                <!-- 选择送审人弹窗 -->
                <el-dialog :close-on-click-modal="false" :visible.sync="entrustedUnitOrgVisible" title="选择送审人"
                  width="50%">
                  <div class="el-dialog-div">
                    <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="false"
                      :is-checked-user="false" :is-filter="true" :is-not-cascade="true" :show-user="true" />
                  </div>
                  <span slot="footer" class="dialog-footer">
                    <el-button class="negative-btn" icon="" @click="entrustedUnitOrgCancel">取消</el-button>
                    <el-button class="active-btn" icon="" type="primary" @click="entrustedUnitSure">确定</el-button>
                  </span>
                </el-dialog>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="送审主题">
                      <el-input v-model="tableQuery.reviewSubject" clearable placeholder="请输入..." />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="送审分类">
                      <el-select v-model="tableQuery.reviewCategory" clearable placeholder="请选择" style="width: 100%">
                        <el-option v-for="item in utils.compliance_report_type" :key="item.dicName"
                          :label="item.dicName" :value="item.dicName" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                  <el-row>
                  <el-col :span="12">
                    <el-form-item label="审查状态">
                      <el-input v-model="tableQuery.dataState" clearable placeholder="请输入..." />
                    </el-form-item>
                  </el-col>
                <el-col :span="12">
                  <el-form-item label="业务领域" >
                    <el-select v-model="tableQuery.businessArea" clearable placeholder="请选择" style="width: 100%">
                      <el-option v-for="item in businessAreaData" :key="item.dicName" :label="item.dicName"
                        :value="item.dicName" />
                    </el-select>
                  </el-form-item>
                </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <!-- <el-form-item label="送审部门">
                      <el-input v-model="tableQuery.createOgnName" placeholder="请选择" class="input-with-select">
                        <el-button slot="append" icon="el-icon-search" @click="showOrgTreeDialog"/>
                      </el-input>
                    </el-form-item> -->
                    <el-form-item label="送审部门" prop="createOgnName">
                      <el-input v-model="tableQuery.createOgnName" class="input-with-select" clearable
                        placeholder="请选择">
                        <el-button slot="append" icon="el-icon-search" @click="chooseNoticeDeptClick" />
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="送审人">
                      <el-input v-model="tableQuery.submitter" class="input-with-select" clearable placeholder="请选择">
                        <el-button slot="append" icon="el-icon-search" @click="choiceEntrustedUnitClick" />
                      </el-input>
                      <!-- <el-input v-model="tableQuery.submitter" placeholder="请选择" class="input-with-select">
                        <el-button slot="append" icon="el-icon-search" @click="showUserTreeDialog"/>
                      </el-input> -->
                      <!-- <span v-else class="viewSpan">{{ mainData.riskDepartment }}</span> -->
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="送审时间">
                      <el-date-picker v-model="tableQuery.submissionDateStart" clearable placeholder="选择日期"
                        style="width: 45%; float: left" type="date" />
                      <div class="label_1" style="width: 10%; float: left; text-align: center"><span>至</span></div>
                      <el-date-picker v-model="tableQuery.submissionDateEnd" clearable placeholder="选择日期"
                        style="width: 45%" type="date" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-button-group style="float: right">
                  <el-button icon="el-icon-search" size="mini" type="primary" @click="search_">搜索</el-button>
                  <el-button icon="el-icon-refresh-left" size="mini" type="primary" @click="empty_">重置</el-button>
                </el-button-group>
              </el-form>
              <!-- el-dialog 组件 -->
              <el-dialog :close-on-click-modal="false" :visible.sync="userDialogVisible" title="选择送审人" width="50%">
                <div class="el-dialog-div">
                  <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="true"
                    :is-checked-user="isCheckedUser" :is-filter="true" :is-not-cascade="true" :show-user="showUser" />
                </div>
                <span slot="footer" class="dialog-footer">
                  <el-button class="negative-btn" icon="" @click="cancel">取消</el-button>
                  <el-button class="active-btn" icon="" type="primary" @click="choiceDeptSure">确定</el-button>
                </span>
              </el-dialog>
              <el-dialog :close-on-click-modal="false" :visible.sync="orgDialogVisible" title="选择送审部门" width="50%">
                <div class="el-dialog-div">
                  <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="true"
                    :is-checked-user="false" :is-filter="true" :is-not-cascade="true" :show-user="false" />
                </div>
                <span slot="footer" class="dialog-footer">
                  <el-button class="negative-btn" icon="" @click="cancel">取消</el-button>
                  <el-button class="active-btn" icon="" type="primary" @click="choiceDeptSure">确定</el-button>
                </span>
              </el-dialog>
              <el-button slot="reference" size="small" type="primary">高级检索</el-button>
            </el-popover>
            <el-button slot="append" icon="el-icon-search" @click="search_" />
          </el-input>
        </div>
      </el-card>
    </el-header>

    <el-main>
      <!-- 选择送审单位 -->
      <SimpleBoardIndex :title="'合规审查台账'">
        <template slot="button">
          <el-button class="normal-btn" size="mini" type="primary" @click="exportExcel">导出Excel</el-button>
        </template>
        <el-table ref="table" v-loading="tableLoading" :data="tableData" :height="table_height"
          :show-overflow-tooltip="true" border fit highlight-current-row row-key="id" size="mini" stripe
          style="table-layout: fixed; width: 100%" @sort-change="tableSort" @row-dblclick="rowDblclick">
          <el-table-column align="center" label="序号" type="index" width="50" />
          <el-table-column v-if="ss.tableColumns.find((item) => item.key === 'reviewSubject').visible" label="事项题目"
            min-width="250" prop="reviewSubject" show-overflow-tooltip />
          <el-table-column v-if="ss.tableColumns.find((item) => item.key === 'reviewCategoryName').visible" label="审查分类"
            min-width="180" prop="reviewCategoryName" show-overflow-tooltip sortable="custom" />
            <el-table-column v-if="ss.tableColumns.find((item) => item.key === 'businessArea').visible" label="业务领域"
            min-width="120" prop="businessArea" show-overflow-tooltip />
          <el-table-column v-if="ss.tableColumns.find((item) => item.key === 'submitter').visible" label="送审人"
            min-width="100" prop="submitter" show-overflow-tooltip />
          <el-table-column v-if="ss.tableColumns.find((item) => item.key === 'createOgnName').visible" label="送审单位"
            min-width="250" prop="createOgnName" show-overflow-tooltip />
          <el-table-column v-if="ss.tableColumns.find((item) => item.key === 'submissionDate').visible" label="送审时间"
            min-width="100" prop="submissionDate" show-overflow-tooltip sortable="custom">
            <template slot-scope="scope">
              <span>{{ scope.row.submissionDate | parseTime('{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="审查状态" min-width="100" prop="dataState" show-overflow-tooltip sortable="custom" />
          <el-table-column align="center" fixed="right" label="操作" width="150">
            <template slot-scope="scope">
              <!-- <el-button v-if="scope.row.dataState == '已保存'" type="text" @click="edit_(scope.$index, scope.row)">
                编辑
              </el-button> -->
              <el-button type="text" @click="view_(scope.$index, scope.row)">查看</el-button>
              <!-- <el-button v-if="scope.row.dataState == '已保存'" type="text" @click="delete_(scope.$index, scope.row)">
                删除
              </el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </SimpleBoardIndex>
    </el-main>
    <el-footer>
      <!--分页工具栏-->
      <pagination :limit.sync="tableQuery.limit" :page.sync="tableQuery.page" :total="tableQuery.total"
        @pagination="refreshData" />
    </el-footer>
  </el-container>
</template>

<script>
// 组件
import pagination from '@/view/components/Pagination/PaginationIndex';
import TableTools from '@/view/components/TableTools/index';
import SimpleBoardIndex from '@/view/components/SimpleBoard/SimpleBoardIndex';
import orgTree from '@/view/components/OrgTree/OrgTree';
// vuex审查状态值
import { mapGetters } from 'vuex';

// 接口api
import complianceReviewApi from '@/api/ComplianceReview/complianceReview.js';
import taskApi from '@/api/_system/task';
import contractApi from '@/api/contract/bmContract'

export default {
  name: 'HgscIndex',
  inject: ['layout'],
  components: { pagination, TableTools, SimpleBoardIndex, orgTree },
  data() {
    return {
      isCheckedUser: false,
      showUser: false,
      deptOrgVisible: false,
      entrustedUnitOrgVisible: false,
      // is_Check: false,
      tableQuery: {
        page: 1,
        limit: 10,
        total: 0,
        reviewSubject: '', // 事项题目
        createOgnName: '', // 送审单位
        reviewCategory: '', // 审查分类
        reviewCategoryName: '', // 审查分类
        submitter: '', // 送审人
        submissionDateStart: '', // 送审开始时间
        submissionDateEnd: '', // 送审结束时间
        fuzzyValue: '', // 模糊搜索值
        dataState:'',//台账专用---不等于该值
        isQuery:true,//是否查询台账
        orgId: '', // 上报单位id
        businessArea:'',//业务领域
      },
      table_height: '100%',
      tableData: [],
      userDialogVisible: false,
      orgDialogVisible: false,
      orgTreeDialog: false,
      zxcheckedData: [],
      orgVisible: false,
      tableLoading: false,
      ss: {
        data: this.tableData,
        tableColumns: [
          { key: 'reviewSubject', label: '事项题目', visible: true },
          { key: 'reviewCategory', label: '审查分类', visible: true },
          { key: 'reviewCategoryName', label: '审查分类', visible: true },
          { key: 'submissionDate', label: '送审时间', visible: true },
          { key: 'submitter', label: '送审人', visible: true },
          { key: 'createOgnName', label: '送审单位', visible: true },
          { key: 'dataState', label: '审查状态', visible: true },
          { key: 'businessArea', label: '业务领域', visible: true },
        ],
      },
      // tableQuery: {
      //   reportYear: '',
      // },
      yearOptions: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i),
      businessAreaData: [],
    };
  },
  computed: {
    ...mapGetters(['orgContext', 'currentFunctionId']),
  },
  activated() {
    // 长连接页面第二次激活的时候,不会走created方法,会走此方法
    this.refreshData();
  },
  created() {
    this.refreshData();
    this.initDic();
  },

  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45;

      // 监听窗口大小变化
      const self = this;
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45;
      };
    });
  },
  methods: {
        initDic() {
      const code = ['businessDomainDic'];
      this.utils.getDic(code).then((response) => {
        this.businessAreaData = response.data.data[code[0]];
      })
    },
    isEdit(row) {
      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataStateCode === this.utils.dataState_BPM.BACK.code;
    },
    isDelete(row) {
      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code;
    },
    // 刷新数据
    refreshData() {
      // 赋值当前人组织全路径
      this.tableQuery.functionCode = this.currentFunctionId.functionCode;
      this.tableQuery.orgId = this.orgContext.currentOrgId;
      this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId;
      // 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页
      if (this.tableData.length === 0 && this.tableQuery.page > 1) {
        this.tableQuery.page--;
      }
      complianceReviewApi
        .query(this.tableQuery)
        .then((response) => {
          let rows = response.data.data.records;
          this.tableData = rows;
          this.ss.data = rows;
          this.tableQuery.total = response.data.data.total;
          this.tableLoading = false;
        })
        .catch({});
    },
    // 点击打开查找机构弹窗
    chooseNoticeDeptClick() {
      this.deptOrgVisible = true;
    },
    // 关闭审查机构弹窗
    deptOrgCancel() {
      this.deptOrgVisible = false;
    },
    // 点击打开送审人弹窗
    choiceEntrustedUnitClick() {
      this.entrustedUnitOrgVisible = true;
    },
    // 点击关闭送审人弹窗
    entrustedUnitOrgCancel() {
      this.entrustedUnitOrgVisible = false;
    },
    entrustedUnitSure() {
      const res = this.zxcheckedData[0];
      console.log(res, 'res');
      this.tableQuery.submitter = res.name;
      this.entrustedUnitOrgVisible = false;
    },
    choiceNoticeDeptSure() {
      let c = '';
      let cid = '';
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.name;
          cid = cid + item.unitId;
        } else {
          c = c + ',' + item.name;
          cid = cid + ',' + item.unitId;
        }
      });
      this.tableQuery.createOgnName = c;
      this.deptOrgVisible = false;
    },
    add_(event) {
      let tableName;
      const name = this.utils.getDicName(this.utils.compliance_review_type, event);
      const tabId = this.utils.createUUID();
      console.log('name:', name);
      console.log('name.id:', name?.id);
      console.log('event:', event);
      if (name ==='重大决策事项合规审查'){
         tableName='hgsc_main_detail';
      }else if (name ==='重要请示事项合规审查'){
         tableName='hgsc_main_detail_qsss';
      }
      else if (name ==='内部规章制度合法合规审查'){
         tableName='hgsc_main_detail_zdhf';
      }
      else if (name ==='特殊经营类合规论证'){
         tableName='hgsc_main_detail_tsjy';
      }
      else if (name ==='合法合规审查意见'){
         tableName='hgsc_main_detail_hfhg';
      }

      this.layout.openNewTab(name, tableName, tableName, tabId, {
        functionId: tableName +',' + tabId,
        ...this.utils.routeState.NEW(tabId),
        reviewCategory: event, reviewCategoryName: name,
      });
    },
    // 编辑
    edit_(index, row) {
      let tableName;
      if (row.reviewCategoryName ==='重大决策事项合规审查'){
        tableName='hgsc_main_detail';
      }else if (row.reviewCategoryName ==='重要请示事项合规审查'){
        tableName='hgsc_main_detail_qsss';
      }
      else if (row.reviewCategoryName ==='内部规章制度合法合规审查'){
        tableName='hgsc_main_detail_zdhf';
      }
      else if (row.reviewCategoryName ==='特殊经营类合规论证'){
        tableName='hgsc_main_detail_tsjy';
      }
      else if (row.reviewCategoryName ==='合法合规审查意见'){
        tableName='hgsc_main_detail_hfhg';
      }
      console.log(row, 'row');
      const tabId = this.utils.createUUID();
      this.layout.openNewTab(row.reviewCategoryName, tableName, tableName, row.id, {
        functionId: tableName+','  + row.id,
        ...this.utils.routeState.EDIT(row.id),
        view: 'old',
        reviewCategory: row.reviewCategory, reviewCategoryName: row.reviewCategoryName
      });
    },
    // 删除
    delete_(index, row) {
      this.$confirm('此操作将永久删除该数据及相关数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          new Promise((resolve, reject) => {
            complianceReviewApi
              .deletebyid({
                id: row.id,
              })
              .then((response) => {
                resolve(response);
              });
          }).then((value) => {
            this.tableData.splice(index, 1);
            this.$message.success('删除成功!');
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          });
        });
    },
    // 查看
    view_(index, row) {
      let tableName;
      if (row.reviewCategoryName ==='重大决策事项合规审查'){
        tableName='hgsc_main_detail';
      }else if (row.reviewCategoryName ==='重要请示事项合规审查'){
        tableName='hgsc_main_detail_qsss';
      }
      else if (row.reviewCategoryName ==='内部规章制度合法合规审查'){
        tableName='hgsc_main_detail_zdhf';
      }
      else if (row.reviewCategoryName ==='特殊经营类合规论证'){
        tableName='hgsc_main_detail_tsjy';
      }
      else if (row.reviewCategoryName ==='合法合规审查意见'){
        tableName='hgsc_main_detail_hfhg';
      }
      if (row.dataStateCode == this.utils.dataState_BPM.SAVE.code) {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab('合同合规审查', tableName, tableName, tabId, {
          functionId: tableName+',' + tabId,
          ...this.utils.routeState.VIEW(row.id),
          reviewCategoryName: row.reviewCategoryName
        });
      } else {
        taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then((res) => {
          const functionId = res.data.data[0].ID;
          const tabId = this.utils.createUUID();
          this.layout.openNewTab('合规审查信息', 'design_page', 'design_page', tabId, {
            processInstanceId: res.data.data[0].PID, //流程实例
            taskId: res.data.data[0].ID, //任务ID
            businessKey: row.id, //业务数据ID
            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
            entranceType: 'FLOWABLE',
            type: 'haveDealt',
            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
            view: 'new',
            reviewCategoryName: row.reviewCategoryName
          });
        });
      }
    },
    rowDblclick(row, column, event) {
      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code) {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab('合规报告审批信息', tableName, tableName, tabId, {
          functionId: tableName+',' + tabId,
          ...this.utils.routeState.VIEW(row.id),
        });
      } else {
        taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then((res) => {
          const functionId = res.data.data[0].ID;
          const tabId = this.utils.createUUID();
          this.layout.openNewTab('合规报告审批信息', 'design_page', 'design_page', tabId, {
            processInstanceId: res.data.data[0].PID, //流程实例
            taskId: res.data.data[0].ID, //任务ID
            businessKey: row.id, //业务数据ID
            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
            entranceType: 'FLOWABLE',
            type: 'haveDealt',
            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
            view: 'new',
          });
        });
      }
    },
    tableSort(column, prop, order) {
      this.tableQuery.sortName = column.prop;
      this.tableQuery.order = column.order === 'ascending';
      this.refreshData();
    },
    // 点击搜索按钮事件,回到第一页,重新刷新数据
    search_: function () {
      this.tableQuery.page = 1;
      this.refreshData();
    },
    // 点击清空按钮事件,清空查询条件属性,回到第一页,重新刷新数据
    empty_() {
      // 清空搜索条件
      this.tableQuery = {
        page: 1,
        limit: 10,
        total: 0,
        riskName: '',
        caseCode: '',
        expectedRiskLevel: '',
        fuzzyValue: '',
      };
      this.refreshData();
    },
    // 点击刷新按钮事件
    refresh_() {
      this.tableQuery.sortName = null;
      this.tableQuery.order = null;
      this.empty_();
    },
    cancel() {
      this.userDialogVisible = false;
    },
    choiceDeptSure() {
      let selectedUnits = this.zxcheckedData.map((item) => item.name).join(', ');
      this.tableQuery.reportingUnit = selectedUnits;
      this.userDialogVisible = false;
    },
        exportExcel() {
      this.exportLoading = true;

      // 显示提示消息
      const loadingMessage = this.$message({
        message: '正在准备导出数据，请稍候...',
        type: 'info',
        duration: 0
      });

      const queryParams = {};
      Object.assign(queryParams, this.selectData);
      queryParams.limit = 9999;

      let date = new Date();
      let formatDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

      contractApi.exportRiskLedger(queryParams).then((response) => {
        const blob = response.data;
        const fileName = formatDate + '合规审查台账.xlsx';

        // 关闭提示消息
        loadingMessage.close();

        // 下载文件
        if ('download' in document.createElement('a')) {
          const elink = document.createElement('a');
          elink.download = fileName;
          elink.style.display = 'none';
          elink.href = URL.createObjectURL(blob);
          document.body.appendChild(elink);
          elink.click();
          URL.revokeObjectURL(elink.href);
          document.body.removeChild(elink);

          this.$message.success('导出成功');
        } else {
          navigator.msSaveBlob(blob, fileName);
        }
      }).catch(error => {
        this.$message.error('导出失败：' + (error.message || '未知错误'));
      }).finally(() => {
        this.exportLoading = false;
      });
    },
  },
};
</script>

<style scoped>
.el-table__fixed-body-wrapper {
  top: 50px !important;
}
</style>
