package com.klaw.utils;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.klaw.entity.ComplianceChack.ComplianceChackEntity;
import com.klaw.entity.authorizationBean.Authorization;
import com.klaw.entity.caseBean.*;
import com.klaw.entity.caseBean.caseRisk.BmCaseEvidence;
import com.klaw.entity.caseBean.caseRisk.BmCaseRisk;
import com.klaw.entity.caseBean.child.Parties;
import com.klaw.entity.complianceReviewBean.ComplianceExamination;
import com.klaw.entity.complianceReviewBean.ComplianceReview;
import com.klaw.entity.complianceReviewBean.ComplianceReviewFixedItem;
import com.klaw.entity.complianceRiskBean.*;
import com.klaw.entity.contractBean.SgSealApproval;
import com.klaw.entity.contractBean.SgSealApprovalDetail;
import com.klaw.entity.contractBean.contract.BmContract;
import com.klaw.entity.contractBean.contract.BmContractBatch;
import com.klaw.entity.contractBean.contract.BmContractClose;
import com.klaw.entity.contractBean.contract.BmContractText;
import com.klaw.entity.lawyerBean.*;
import com.klaw.entity.mainDataBean.SgProjectManage;
import com.klaw.entity.project.ProjectMajorDetail;
import com.klaw.entity.project.ProjectMajorReasoning;
import com.klaw.entity.project.ProjectMajorSummary;
import com.klaw.entity.specialBean.ProjectDemonstration;
import com.klaw.service.ComplianceChackService.ComplianceChackService;
import com.klaw.service.authorizationService.AuthorizationService;
import com.klaw.service.caseService.*;
import com.klaw.service.caseService.bmCaseRiskService.BmCaseRiskService;
import com.klaw.service.caseService.childService.PartiesService;
import com.klaw.service.complianceReviewService.ComplianceExaminationService;
import com.klaw.service.complianceReviewService.ComplianceReviewFixedItemService;
import com.klaw.service.complianceReviewService.ComplianceReviewService;
import com.klaw.service.complianceRiskService.*;
import com.klaw.service.contractService.SgProjectManageService;
import com.klaw.service.contractService.SgSealApprovalDetailService;
import com.klaw.service.contractService.SgSealApprovalService;
import com.klaw.service.contractService.contract.BmContractBatchService;
import com.klaw.service.contractService.contract.BmContractCloseService;
import com.klaw.service.contractService.contract.BmContractService;
import com.klaw.service.contractService.contract.BmContractTextService;
import com.klaw.service.lawyerService.*;
import com.klaw.service.projectService.ProjectMajorDetailService;
import com.klaw.service.projectService.ProjectMajorReasoningService;
import com.klaw.service.projectService.ProjectMajorSummaryService;
import com.klaw.service.specialService.ProjectDemonstrationService;
import com.sgai.mcp.flow.bpmn.model.ProcessExt;
import com.sgai.mcp.flow.bpmn.model.usertask.UserTaskExt;
import com.sgai.mcp.flow.bpmn.model.usertask.customPropertiesDto;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class UniAppLoginUtil {

    private static final Logger log = LoggerFactory.getLogger(UniAppLoginUtil.class);
    @Resource
    LawyerOutApprovalDetailService lawyerOutApprovalDetailService;
    @Resource
    private BmContractService bmContractService;
    @Resource
    private BmContractBatchService bmContractBatchService;
    @Resource
    private BmContractTextService bmContractTextService;
    @Resource
    private AuthorizationService authorizationService;
    @Resource
    private SgSealApprovalService sgSealApprovalService;
    @Resource
    private SgSealApprovalDetailService sgSealApprovalDetailService;
    @Autowired
    private CaseProsecutionService caseProsecutionService;
    @Resource
    private SgCaseSuedService sgCaseSuedService;
    @Resource
    private CaseExamineService caseExamineService;
    @Resource
    private SgCaseSuperviseService sgCaseSuperviseService;
    @Autowired
    private LawFirmOutApprovalMainService lawFirmOutApprovalMainService;
    @Autowired
    private LawFirmBlackListService lawFirmBlackListService;
    @Autowired
    private LawFirmSelectionService lawFirmSelectionService;
    @Autowired
    private SgProjectManageService sgProjectManageService;
    @Autowired
    private CaseRiskService caseRiskService;
    @Autowired
    private BmCaseRiskService bmCaseRiskService;
    @Autowired
    private LawyerOutApprovalMainService lawyerOutApprovalMainService;
    @Autowired
    private LawyerBlackListService lawyerBlackListService;
    @Autowired
    private PartiesService partiesService;
    @Autowired
    private LawFirmOutApprovalDetailService lawFirmOutApprovalDetailService;
    @Autowired
    private BmContractCloseService bmContractCloseService;
    @Autowired
    private InvestmentProjectRiskReviewService investmentProjectRiskReviewService;
    @Autowired
    private ComplianceProcessService complianceProcessService;
    @Autowired
    private RiskApprovalProcessService riskApprovalProcessService;
    @Autowired
    private ComplianceReportService complianceReportService;
    @Autowired
    private ComplianceRiskWaringService complianceRiskWaringService;
    @Autowired
    private ComplianceRiskMitigationService complianceRiskMitigationService;
    @Autowired
    private ComplianceAccountabilityService complianceAccountabilityService;
    @Autowired
    private ComplianceChackService complianceChackService;
    @Autowired
    private ProjectDemonstrationService projectDemonstrationService;
    @Autowired
    private ProjectMajorSummaryService projectMajorSummaryService;
    @Autowired
    private ProjectMajorReasoningService projectMajorReasoningService;
    @Autowired
    private ProjectMajorDetailService projectMajorDetailService;
    @Autowired
    private ComplianceReviewService complianceReviewService;
    @Autowired
    private ComplianceExaminationService complianceExaminationService;
    @Autowired
    private ComplianceReviewFixedItemService complianceReviewFixedItemService;
    @Resource
    private TaskService taskService;
    @Resource
    private RuntimeService runtimeService;
    public JSONObject getDataInfo(JSONObject jsonObject) {
        String functionCode = jsonObject.getString("functionCode");
        String id = jsonObject.getString("id");
        JSONObject Json = new JSONObject();
        if (StringUtils.isBlank(functionCode) || StringUtils.isBlank(id)) {
            Json.put("Error", "functionCode或id不存在");
            return Json;
        }
        if (StringUtils.isNotBlank(functionCode)) {
            switch (functionCode) {
                //内部项目立案审批
                case "internal_project_main":
                    SgProjectManage sgProjectManage = sgProjectManageService.getById(id);
                    if (sgProjectManage == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("id", sgProjectManage.getCode());
                    Json.put("projectName", sgProjectManage.getProjectName());//项目名称
                    Json.put("projectCode", sgProjectManage.getProjectCode());//项目编码
                    Json.put("createPsnFullName", sgProjectManage.getCreatePsnFullName());//经办人
                    Json.put("createTime", sgProjectManage.getCreateTime());//经办时间
                    Json.put("projectInvestment", sgProjectManage.getProjectInvestment());//总投入(元)
                    Json.put("projectUnit", sgProjectManage.getProjectUnit());//项目责任单位
                    Json.put("remarks", sgProjectManage.getRemarks());//项目说明
                    Json.put("attachment", sgProjectManage.getAttachment());//其他附件
                    break;
                //新增合同、合同变更、合同终止
                case "contract_approval_main":
                case "contract_change_main":
                case "contract_stop_main":
                    BmContract byId = bmContractService.getById(id);
                    if (byId == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("contractType", byId.getContractType());//合同分类
                    Json.put("contractTypeCode", byId.getContractTypeCode());//合同分类
                    Json.put("id", byId.getId());
                    Json.put("contractName", byId.getContractName());//合同名称
                    Json.put("approvalCode", byId.getApprovalCode());//审批单号
                    Json.put("createPsnFullName", byId.getCreatePsnFullName());//经办人
                    Json.put("createPsnId", byId.getCreatePsnId());//主数据ID
                    Json.put("createTime", byId.getCreateTime());//经办时间
                    Json.put("createPsnPhone", byId.getCreatePsnPhone());//经办人电话
                    Json.put("businessLeaders", byId.getBusinessLeaders());//业务负责人考核意见
                    Json.put("departmentHeads", byId.getDepartmentHeads());//部门负责人考核意见
                    Json.put("assessmentScore", byId.getAssessmentScore());//考核分数
                    JSONArray jsonTexts = new JSONArray();

                    JSONArray jsonFiles = new JSONArray();
                    if (StringUtils.isNotBlank(byId.getContractFiles())) {
                        jsonFiles.addAll(JSON.parseArray(byId.getContractFiles()));
                    }
                    JSONArray jsonComplianceFiles = new JSONArray();
                    if (StringUtils.isNotBlank(byId.getComplianceFiles())) {
                        jsonComplianceFiles.addAll(JSON.parseArray(byId.getComplianceFiles()));
                    }
                    Json.put("complianceFiles", jsonComplianceFiles);//附件
                    List<BmContractText> list = bmContractTextService.list(new QueryWrapper<BmContractText>().eq("parent_Id", byId.getId()).isNotNull("attachment"));
                    if (!list.isEmpty()) {
                        list.forEach(item -> {
                            JSONArray objects = JSON.parseArray(item.getAttachment());
                            jsonTexts.addAll(objects);
                        });
                    }
                    //新增合同
                    if ("contract_approval_main".equals(functionCode)) {
                        Json.put("contractMoney", byId.getContractMoney());//合同金额(元)
                        Json.put("contractMoneyRmb", byId.getContractMoneyRmb());//合同金额(元)
                        Json.put("projectName", byId.getProjectNames());//项目名称
                        Json.put("ourPartyName", byId.getOurPartyName());//我方当事人
                        Json.put("otherPartyName", byId.getOtherPartyName());//对方当事人
                        Json.put("summaryNote", byId.getSummaryNote());//摘要说明
                        Json.put("contractFile", jsonFiles);//附件

                    }
                    //合同变更
                    if ("contract_change_main".equals(functionCode)) {
                        Json.put("contractMoneyRmb", byId.getContractMoneyRmb());//合同金额(元)
                        Json.put("thisChangeMoney", byId.getThisChangeMoney());//变更合同金额(元)
                        Json.put("originalContractName", byId.getOriginalContractName());//原合同名称
                        Json.put("projectName", byId.getProjectNames());//项目名称
                        Json.put("ourPartyName", byId.getOurPartyName());//我方当事人
                        Json.put("otherPartyName", byId.getOtherPartyName());//对方当事人
                        Json.put("summaryNote", byId.getSummaryNote());//变更说明
                        Json.put("contractFile", jsonFiles);//附件
                    }
                    //合同终止
                    if ("contract_stop_main".equals(functionCode)) {
                        Json.put("contractMoneyRmb", byId.getContractMoneyRmb());//合同金额(元)
                        Json.put("originalContractName", byId.getOriginalContractName());//原合同名称
                        Json.put("contractExecutoryMoney", byId.getContractExecutoryMoney());//待履行金额(元)
                        Json.put("projectName", byId.getProjectNames());//项目名称
                        Json.put("ourPartyName", byId.getOurPartyName());//我方当事人
                        Json.put("otherPartyName", byId.getOtherPartyName());//对方当事人
                        Json.put("summaryNote", byId.getExplain());//终止说明
                    }
                    Json.put("otherAttachments", jsonTexts);//合同文本
                    Json.put("isAtemplate", byId.getIsAtemplate());//是否范本

                    //授权委托书
                    Authorization authorization = authorizationService.getById(byId.getAuthorizedId());
                    if (authorization != null) {
                        Json.put("authorizedBook", StringUtils.isNotBlank(authorization.getAuthTakeEffectFile()) ? authorization.getAuthTakeEffectFile() : "空");
                    } else {
                        Json.put("authorizedBook", "空");
                    }
                    Json.put("parseCompliance", false);//是否合规节点
                    String processInstanceId = jsonObject.getString("processInstanceId");
                    if (processInstanceId == null) {
                        break;
                    }
                    try {
                        Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).list().get(0);
                        ProcessExt process = runtimeService.getVariable(task.getExecutionId(), ProcessExt.VARIABLE_NAME, ProcessExt.class);
                        UserTaskExt oneUserTaskExt = process.getUserTaskByActivityId(task.getTaskDefinitionKey());
                        getParse(Json, oneUserTaskExt);
                    }catch (Exception e){
                        Json.put("parseCompliance", false);
                    }

                    break;

                //合同合并
                case "contract_more_main":
                    BmContractBatch bmContractBatch = bmContractBatchService.getById(id);
                    if (bmContractBatch == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    bmContractBatch.setContractList(bmContractService.list(new QueryWrapper<BmContract>().eq("parent_id", bmContractBatch.getId())));
                    Json.put("matterName", bmContractBatch.getMatterName());
                    Json.put("approvalCode", bmContractBatch.getApprovalCode());
                    Json.put("createPsnFullName", bmContractBatch.getCreatePsnFullName());
                    Json.put("createPsnId", bmContractBatch.getCreatePsnId());//主数据ID
                    Json.put("createTime", bmContractBatch.getCreateTime());
                    Json.put("contractType", bmContractBatch.getContractType());
                    Json.put("contractNumber", bmContractBatch.getContractNumber());
                    Json.put("summaryNot", bmContractBatch.getSummaryNote());
                    Json.put("contractFile", bmContractBatch.getContractFile());
                    Json.put("businessLeaders", bmContractBatch.getBusinessLeaders());//业务负责人考核意见
                    Json.put("departmentHeads", bmContractBatch.getDepartmentHeads());//部门负责人考核意见
                    Json.put("assessmentScore", bmContractBatch.getAssessmentScore());//考核分数
                    JSONArray chComplianceFiles = new JSONArray();
                    if (StringUtils.isNotBlank(bmContractBatch.getComplianceFiles())) {
                        chComplianceFiles.addAll(JSON.parseArray(bmContractBatch.getComplianceFiles()));
                    }
                    Json.put("complianceFiles", chComplianceFiles);//附件
                    if (bmContractBatch.getContractList() != null && !bmContractBatch.getContractList().isEmpty()) {
                        Json.put("isAtemplate", bmContractBatch.getIsAtemplate());//是否范本
                        BigDecimal reduce = bmContractBatch.getContractList().stream()
                                .map(BmContract::getAfterChangeAmountRmb)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        Json.put("totalMoney", reduce);//总金额

                        if ("关联授权".equals(bmContractBatch.getContractList().get(0).getAuthorizedSource())) {
                            Authorization authorization2 = authorizationService.getById(bmContractBatch.getContractList().get(0).getAuthorizedId());
                            if (authorization2 != null) {
                                Json.put("authorizedBook", StringUtils.isNotBlank(authorization2.getAuthTakeEffectFile()) ? authorization2.getAuthTakeEffectFile() : "空");
                            }
                        }
                    }
                    Json.put("parseCompliance", false);//是否合规节点
                    String processInstanceId1 = jsonObject.getString("processInstanceId");
                    if (processInstanceId1 == null) {
                        break;
                    }
                    Task task1 = taskService.createTaskQuery().processInstanceId(processInstanceId1).list().get(0);
                    ProcessExt process1 = runtimeService.getVariable(task1.getExecutionId(), ProcessExt.VARIABLE_NAME, ProcessExt.class);
                    UserTaskExt oneUserTaskExt1 = process1.getUserTaskByActivityId(task1.getTaskDefinitionKey());
                    getParse(Json, oneUserTaskExt1);
                    break;

                //主诉事项决策
                case "case_prosecution_main":
                    CaseProsecution caseProsecution = caseProsecutionService.getById(id);
                    if (caseProsecution == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("caseName", caseProsecution.getCaseName());//事项名称
                    Json.put("caseCode", caseProsecution.getCaseCode());//审批单号
                    Json.put("createPsnFullName", caseProsecution.getCreatePsnFullName());//组织名称
                    Json.put("createPsnId", caseProsecution.getCreatePsnId());//主数据ID
                    Json.put("createTime", caseProsecution.getCreateTime());//经办时间
                    Json.put("createPsnPhone", caseProsecution.getCreatePsnPhone());//经办人电话
                    List<Parties> partiesList = partiesService.list(new QueryWrapper<Parties>().eq("master_Id", id));
                    String collect = partiesList.stream().filter(a -> "被告".equals(a.getPartyType())).map(Parties::getParty).collect(Collectors.joining(","));

                    Json.put("party", collect);
                    Json.put("caseInterest", caseProsecution.getCaseInterest());//案件金额-利息(元)
                    Json.put("caseMoney", caseProsecution.getCaseMoney());//案件金额
                    Json.put("des", caseProsecution.getDes());//摘要说明
                    JSONArray caseArray = new JSONArray();
                    if (StringUtils.isNotBlank(caseProsecution.getFiles())) {
                        caseArray.addAll(JSON.parseArray(caseProsecution.getFiles()));
                    }
                    Json.put("files", caseArray);//附件资料
                    break;

                //被诉告知
                case "case_sued_main":
                    CaseSued caseSued = sgCaseSuedService.getById(id);
                    if (caseSued == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("caseName", caseSued.getCaseName());//事项名称
                    Json.put("caseCode", caseSued.getCaseCode());//审批单号
                    Json.put("createPsnFullName", caseSued.getCreatePsnFullName());//组织名称
                    Json.put("createPsnId", caseSued.getCreatePsnId());//主数据ID
                    Json.put("createTime", caseSued.getCreateTime());//经办时间
                    Json.put("createPsnPhone", caseSued.getCreatePsnPhone());//经办人电话
                    Json.put("caseMoney", caseSued.getCaseMoney());//案件金额
                    Json.put("caseInterest", caseSued.getCaseInterest());//案件金额-利息(元)
                    List<Parties> partiesList1 = partiesService.list(new QueryWrapper<Parties>().eq("master_Id", id));
                    String collect1 = partiesList1.stream().filter(a -> "被告".equals(a.getPartyType())).map(Parties::getParty).collect(Collectors.joining(","));
                    Json.put("party", collect1);
                    Json.put("des", caseSued.getDes());//摘要说明
                    JSONArray caseFileArray = new JSONArray();
                    if (StringUtils.isNotBlank(caseSued.getReportFiles())) {
                        caseFileArray.addAll(JSON.parseArray(caseSued.getReportFiles()));
                    }
                    Json.put("files", caseFileArray);//附件资料
                    break;

                //案件过程审批
                case "case_examine_main":
                    CaseExamine caseExamine = caseExamineService.getById(id);
                    if (caseExamine == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("caseName", caseExamine.getItemName());//事项名称
                    Json.put("caseCode", caseExamine.getItemNumber());//事项单号
                    Json.put("createPsnFullName", caseExamine.getCreatePsnFullName());//经办人
                    Json.put("createPsnId", caseExamine.getCreatePsnId());//主数据ID
                    Json.put("createTime", caseExamine.getCreateTime());//经办时间
//                    Json.put("createPsnPhone", caseExamine.getCreatePsnPhone());//经办人电话
                    Json.put("caseMoney", caseExamine.getCaseMoney());//案件金额
                    Json.put("caseInterest", caseExamine.getCaseInterest());//案件金额-利息(元)
                    Json.put("party", caseExamine.getOtherPartys());//对方当事人
                    Json.put("content", caseExamine.getContent());//摘要说明

                    JSONArray ExamineArray = new JSONArray();
                    if (StringUtils.isNotBlank(caseExamine.getAppendix())) {
                        ExamineArray.addAll(JSON.parseArray(caseExamine.getAppendix()));
                    }
                    Json.put("appendix", ExamineArray);//附件资料
                    break;
                //案件风险告知审批单
                case "case_risk_main":
                    CaseRisk caseRisk = caseRiskService.queryDataById(id);
                    if (caseRisk == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("caseName", caseRisk.getRiskName());//案件名称
                    Json.put("caseCode", caseRisk.getCaseCode());//事项单号
                    Json.put("createPsnFullName", caseRisk.getCreatePsnFullName());//经办人
//                    Json.put("createPsnId", caseRisk.getCreatePsnId());//主数据ID
                    Json.put("createTime", caseRisk.getCreateTime());//经办时间
                    Json.put("createPsnPhone", caseRisk.getCreatePsnPhone());//经办人电话
                    Json.put("party", caseRisk.getParty());//对方当事人
                    Json.put("caseMoney", caseRisk.getInvolvedAmount());//案件金额-本金(元)
                    Json.put("caseInterest", caseRisk.getCaseInterest());//案件金额-利息(元)
                    Json.put("content", caseRisk.getRiskDescription());//风险说明
                    JSONArray relateArray = new JSONArray();
                    if (StringUtils.isNotBlank(caseRisk.getRelatedAttachments())) {
                        relateArray.addAll(JSON.parseArray(caseRisk.getRelatedAttachments()));
                    }
                    Json.put("files", relateArray);//附件资料
                    break;
                //案件风控审批流程
                case "case_risks_main":
                    BmCaseRisk bmCaseRisk = bmCaseRiskService.queryDataById(id);
                    if (bmCaseRisk == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("createPsnFullName", bmCaseRisk.getCreatePsnFullName());//经办人
                    Json.put("createTime", bmCaseRisk.getCreateTime());//经办时间
                    Json.put("caseName", bmCaseRisk.getCaseName());//案件名称
                    Json.put("caseCode", bmCaseRisk.getCaseCode());//事项编号
                    Json.put("causeName", bmCaseRisk.getCauseName());//案由
                    Json.put("caseNumber", bmCaseRisk.getCaseNumber());//立案案号
                    Json.put("organizeTime", bmCaseRisk.getOrganizeTime());//承办时间
                    Json.put("organizePsnName", bmCaseRisk.getOrganizePsnName());//承办人
                    Json.put("caseTime", bmCaseRisk.getCaseTime());//发案时间
                    Json.put("caseAbstract", bmCaseRisk.getCaseAbstract());//案情摘要
                    Json.put("mainBody", bmCaseRisk.getMainBody());//主体
                    Json.put("court", bmCaseRisk.getCourt());//管辖
                    Json.put("program", bmCaseRisk.getProgram());//程序
                    JSONArray evidenceAttachment = new JSONArray();
                    if (StringUtils.isNotBlank(bmCaseRisk.getEvidenceAttachment())) {
                        evidenceAttachment.addAll(JSON.parseArray(bmCaseRisk.getEvidenceAttachment()));
                    }
                    Json.put("evidenceAttachment", evidenceAttachment);//证据附件
                    Json.put("legalStrategy", bmCaseRisk.getLegalStrategy());//法律策略
                    Json.put("responsePlan", bmCaseRisk.getResponsePlan());//应诉方案
                    JSONArray measureAttachment = new JSONArray();
                    if (StringUtils.isNotBlank(bmCaseRisk.getMeasureAttachment())) {
                        measureAttachment.addAll(JSON.parseArray(bmCaseRisk.getMeasureAttachment()));
                    }
                    Json.put("measureAttachment", measureAttachment);//措施附件
                    Json.put("riskState", bmCaseRisk.getRiskState());//风控状态
                    Json.put("riskStateCode", bmCaseRisk.getRiskStateCode());//风控状态code
                    Json.put("riskType", bmCaseRisk.getRiskType());//风控类型
                    Json.put("riskTypeCode", bmCaseRisk.getRiskTypeCode());//风控类型code
                    Json.put("processingResults", bmCaseRisk.getProcessingResults());//办理结果
                    Json.put("businessLeaders", bmCaseRisk.getBusinessLeaders());//业务负责人考核意见
                    Json.put("departmentHeads", bmCaseRisk.getDepartmentHeads());//部门负责人考核意见
                    Json.put("assessmentScore", bmCaseRisk.getAssessmentScore());//考核分数
                    List<BmCaseEvidence> ours = bmCaseRisk.getEvidenceList().stream().filter(evidence -> "我方证据".equals(evidence.getEvidenceType())).collect(Collectors.toList());
                    List<BmCaseEvidence> others = bmCaseRisk.getEvidenceList().stream().filter(evidence -> "对方证据".equals(evidence.getEvidenceType())).collect(Collectors.toList());
                    Json.put("ours", ours);//我方证据
                    Json.put("others", others);//对方证据
                    Json.put("parseCompliance", false);//是否合规节点
                    String processInstanceId2 = jsonObject.getString("processInstanceId");
                    if (processInstanceId2 == null) {
                        break;
                    }
                    Task task2 = taskService.createTaskQuery().processInstanceId(processInstanceId2).list().get(0);
                    ProcessExt process2 = runtimeService.getVariable(task2.getExecutionId(), ProcessExt.VARIABLE_NAME, ProcessExt.class);
                    UserTaskExt oneUserTaskExt2 = process2.getUserTaskByActivityId(task2.getTaskDefinitionKey());
                    getParse(Json, oneUserTaskExt2);
                    break;
                //重大案件领导督办
                case "case_supervise_main":
                    SgCaseSupervise caseSupervise = sgCaseSuperviseService.getById(id);
                    if (caseSupervise == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("title", caseSupervise.getTitle());//事项名称
                    Json.put("superviseCode", caseSupervise.getSuperviseCode());//审批单号
                    Json.put("createPsnFullName", caseSupervise.getCreatePsnFullName());//组织名称
                    Json.put("createPsnId", caseSupervise.getCreatePsnId());//主数据ID
                    Json.put("createTime", caseSupervise.getCreateTime());//经办时间
//                    Json.put("caseMoney", caseSupervise.getInvolvedAmount());//案件金额-本金(元)
//                    Json.put("caseInterest", caseSupervise.getCaseInterest());//案件金额-利息(元)
                    Json.put("party", caseSupervise.getOtherParties());//对方当事人
                    Json.put("superviseType", caseSupervise.getSuperviseType());//督办类型
                    Json.put("description", caseSupervise.getDescription());//摘要说明
                    JSONArray attachmentSuedArray = new JSONArray();
                    if (StringUtils.isNotBlank(caseSupervise.getAttachment())) {
                        attachmentSuedArray.addAll(JSON.parseArray(caseSupervise.getAttachment()));
                    }
                    Json.put("attachment", attachmentSuedArray);//附件资料
                    break;
                //授权
                case "authorization_main":
                case "sublicense_main":
                case "change_main":
                case "termination_main":
                case "authorization_contract_main":
                case "shareholder_main":
                case "litigation_main":
                case "routine_main":
                    Authorization authorization1 = authorizationService.getById(id);
                    if (authorization1 == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("authorizedName", authorization1.getAuthorizedName());//授权名称
                    Json.put("authorizedBookNumber", authorization1.getAuthorizedBookNumber());//审批单号
                    Json.put("createPsnFullName", authorization1.getCreatePsnFullName());//经办人
                    Json.put("createPsnId", authorization1.getCreatePsnId());//主数据ID
                    Json.put("createTime", authorization1.getCreateTime());//经办时间
                    Json.put("createPsnPhone", authorization1.getCreatePsnPhone());//经办人电话
                    Json.put("authorizer", authorization1.getAuthorizer());//委托人
                    Json.put("trustee", authorization1.getTrustee());//被授权人
                    Json.put("trusteePosition", authorization1.getTrusteePosition());//被授权人的职位
                    Json.put("trusteeId", authorization1.getTrusteeId());//被授权人身份证号
                    Json.put("trusteeCompany", authorization1.getTrusteeCompany());//被授权人单位
                    if ("shareholder_main".equals(functionCode) || "routine_main".equals(functionCode)) {
                        Json.put("startTime", authorization1.getStartTime());//授权开始时间
                        Json.put("endTime", authorization1.getEndTime());//授权结束时间
                    } else {
                        Json.put("startTime", authorization1.getStartTimeStr());//授权开始时间
                    }
                    Json.put("entrustedMatters", authorization1.getEntrustedMatters());//授权事项及说明
                    JSONArray jsonArray1 = new JSONArray();
                    if (StringUtils.isNotBlank(authorization1.getAttachment())) {
                        jsonArray1.addAll(JSON.parseArray(authorization1.getAttachment()));
                    }
                    Json.put("attachment", jsonArray1);//授权书及附件
                    JSONArray jsonArray2 = new JSONArray();
                    if (StringUtils.isNotBlank(authorization1.getAuthorizedBook())) {
                        jsonArray2.addAll(JSON.parseArray(authorization1.getAuthorizedBook()));
                    }
                    Json.put("authorizedBook", jsonArray2);//授权委托书
                    if ("termination_main".equals(functionCode)) {
                        Json.put("authorizationPeriod", authorization1.getAuthorizationPeriod());//终止原因
                        Json.put("sublicensePerson", authorization1.getSublicensePerson());//授权审批人
                        Json.put("remarks", authorization1.getRemarks());//备注
                    }
                    break;

                //律所出库审批单
                case "firm_out_main":
                    LawFirmOutApprovalMain lawMain = lawFirmOutApprovalMainService.getById(id);
                    if (lawMain == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("itemName", lawMain.getItemName());//事项名称
                    Json.put("sequenceCode", lawMain.getSequenceCode());//事项编号
                    Json.put("createPsnFullName", lawMain.getCreatePsnFullName());//经办组织
                    Json.put("createTime", lawMain.getCreateTime());//经办时间
                    List<LawFirmOutApprovalDetail> lawFirmOutList = lawFirmOutApprovalDetailService.list(new QueryWrapper<LawFirmOutApprovalDetail>().eq("parent_id", id));
                    String collect3 = lawFirmOutList.stream().map(LawFirmOutApprovalDetail::getLawyerFirm).collect(Collectors.joining(","));
                    Json.put("lawyerFirm", collect3);
                    Json.put("description", lawMain.getFailName());//出库原因
                    //Json.put("description",lawMain.getDescription());//详细描述
                    JSONArray LawFirmOutArray = new JSONArray();
                    if (StringUtils.isNotBlank(lawMain.getAttachment())) {
                        LawFirmOutArray.addAll(JSON.parseArray(lawMain.getAttachment()));
                    }
                    Json.put("attachment", LawFirmOutArray);//附件资料
                    break;

                //律师出库审批单
                case "lawyer_out_main":
                    LawyerOutApprovalMain lawyerOutApprovalMain = lawyerOutApprovalMainService.getById(id);
                    if (lawyerOutApprovalMain == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("itemName", lawyerOutApprovalMain.getItemName());//事项名称
                    Json.put("sequenceCode", lawyerOutApprovalMain.getSequenceCode());//事项编号
                    Json.put("createPsnFullName", lawyerOutApprovalMain.getCreatePsnFullName());//经办组织
                    Json.put("createTime", lawyerOutApprovalMain.getCreateTime());//经办时间

                    List<LawyerOutApprovalDetail> lawyerOutList = lawyerOutApprovalDetailService.list(new QueryWrapper<LawyerOutApprovalDetail>().eq("parent_Id", lawyerOutApprovalMain.getId()));
                    String collect4 = lawyerOutList.stream().map(LawyerOutApprovalDetail::getLawyerName).collect(Collectors.joining(","));
                    String collect5 = lawyerOutList.stream().map(LawyerOutApprovalDetail::getLawFirm).collect(Collectors.joining(","));
                    Json.put("lawyerName", collect4);//律师姓名
                    Json.put("lawyerFirm", collect5);//所属律所
                    Json.put("description", lawyerOutApprovalMain.getDescription());//出库说明

                    JSONArray lawyerOutArray = new JSONArray();
                    if (StringUtils.isNotBlank(lawyerOutApprovalMain.getAttachment())) {
                        lawyerOutArray.addAll(JSON.parseArray(lawyerOutApprovalMain.getAttachment()));
                    }
                    Json.put("attachment", lawyerOutArray);//附件资料
                    break;

                //律所黑名单审批单
                case "firm_black_main":
                    LawFirmBlackList lawFirmBlack = lawFirmBlackListService.getById(id);
                    if (lawFirmBlack == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("itemName", lawFirmBlack.getItemName());//事项名称
                    Json.put("sequenceCode", lawFirmBlack.getSequenceCode());//事项编号
                    Json.put("createPsnFullName", lawFirmBlack.getCreatePsnFullName());//经办组织
                    Json.put("createTime", lawFirmBlack.getCreateTime());//经办时间
                    Json.put("blacklistReason", lawFirmBlack.getBlacklistReason());//加入黑名单原因
                    Json.put("lawyerFirm", lawFirmBlack.getLawyerFirm());//律所名称

                    JSONArray blackArray = new JSONArray();
                    if (StringUtils.isNotBlank(lawFirmBlack.getBusinessLicense())) {
                        blackArray.addAll(JSON.parseArray(lawFirmBlack.getBusinessLicense()));
                    }
                    Json.put("businessLicense", blackArray);//附件资料
                    break;

                //律师黑名单审批单
                case "lawyer_black_list_main":
                    LawyerBlackList blackList = lawyerBlackListService.getById(id);
                    if (blackList == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("itemName", blackList.getItemName());//事项名称
                    Json.put("sequenceCode", blackList.getSequenceCode());//事项编号
                    Json.put("createPsnFullName", blackList.getCreatePsnFullName());//经办人
                    Json.put("createTime", blackList.getCreateTime());//经办时间
                    Json.put("lawyerName", blackList.getLawyerName());//律师名称
                    Json.put("lawyerFirm", blackList.getLawFirm());//所属律所
                    Json.put("blacklistReason", blackList.getBlackReason());//加入黑名单说明
                    JSONArray blackListArray = new JSONArray();
                    if (StringUtils.isNotBlank(blackList.getAttachment())) {
                        blackListArray.addAll(JSON.parseArray(blackList.getAttachment()));
                    }
                    Json.put("businessLicense", blackListArray);//附件资料
                    break;

                //律所直接委托审批表
                case "firm_select_main_compare":
                    LawFirmSelection lawFirm = lawFirmSelectionService.getById(id);
                    if (lawFirm == null) {
                        Json.put("data", "没有该id");//事项名称
                        break;
                    }
                    Json.put("title", lawFirm.getTitle());//事项名称
                    Json.put("sequenceCode", lawFirm.getSequenceCode());//审批单号
                    Json.put("createPsnFullName", lawFirm.getCreatePsnFullName());//组织名称
                    Json.put("createPsnId", lawFirm.getCreatePsnId());//主数据ID
                    Json.put("createTime", lawFirm.getCreateTime());//经办时间
                    Json.put("selectionModeName", lawFirm.getSelectionModeName());//选聘方式
                    Json.put("selectionTypeName", lawFirm.getSelectionTypeName());//外聘类型
                    Json.put("remark", lawFirm.getRemark());//摘要说明

                    JSONArray LawFirmArray1 = new JSONArray();
                    if (StringUtils.isNotBlank(lawFirm.getOtherAttachment())) {
                        LawFirmArray1.addAll(JSON.parseArray(lawFirm.getOtherAttachment()));
                    }
                    Json.put("otherAttachment", LawFirmArray1);//附件资料
                    break;

                //用印审批
                case "seal_approval_main":
                    SgSealApproval seal = sgSealApprovalService.getById(id);
                    Json.put("id", seal.getId());
                    Json.put("sealPurpose", seal.getSealPurpose());//盖章用途
                    Json.put("contractName", seal.getContractName());//合同名称
                    Json.put("createPsnFullName", seal.getCreatePsnFullName());//经办人
                    Json.put("createPsnId", seal.getCreatePsnId());//主数据ID
                    Json.put("createTime", seal.getCreateTime());//经办时间
                    List<SgSealApprovalDetail> textList = sgSealApprovalDetailService.list(new QueryWrapper<SgSealApprovalDetail>().eq("parent_Id", seal.getId()));
                    if (textList != null) {
                        String names = textList.stream().map(SgSealApprovalDetail::getSealName).collect(Collectors.joining(","));
                        Json.put("sealName", names);//印章名称
                        String types = textList.stream().map(SgSealApprovalDetail::getSealType).collect(Collectors.joining(","));
                        Json.put("sealType", types);//印章类型
                        String sealAdminOld = textList.stream().map(SgSealApprovalDetail::getSealAdminOld).collect(Collectors.joining(","));
                        Json.put("sealAdminOld", sealAdminOld);//印章管理员
                        Json.put("sealNumberOld", textList.get(0).getSealNumberOld());//申请时的用印份数
                        Json.put("printsNumberOld", textList.get(0).getPrintsNumberOld());//申请时的用印枚数
                    }
                    break;

                //补充用印审批
                case "supp_seal_approval_main":
                    SgSealApproval seal1 = sgSealApprovalService.getById(id);
                    Json.put("id", seal1.getId());
                    Json.put("sealPurpose", seal1.getSealPurpose());//盖章用途
                    Json.put("contractName", seal1.getContractName());//合同名称
                    Json.put("createPsnFullName", seal1.getCreatePsnFullName());//经办人
                    Json.put("createPsnId", seal1.getCreatePsnId());//主数据ID
                    Json.put("createTime", seal1.getCreateTime());//经办时间
                    List<SgSealApprovalDetail> textList1 = sgSealApprovalDetailService.list(new QueryWrapper<SgSealApprovalDetail>().eq("parent_Id", seal1.getId()));
                    if (textList1 != null) {
                        String names = textList1.stream().map(SgSealApprovalDetail::getSealName).collect(Collectors.joining(","));
                        Json.put("sealName", names);//印章名称
                        String types = textList1.stream().map(SgSealApprovalDetail::getSealType).collect(Collectors.joining(","));
                        Json.put("sealType", types);//印章类型
                        String sealAdminOld = textList1.stream().map(SgSealApprovalDetail::getSealAdminOld).collect(Collectors.joining(","));
                        Json.put("sealAdminOld", sealAdminOld);//印章管理员
                        Json.put("sealNumberOld", textList1.get(0).getSealNumberOld());//申请时的用印份数
                        Json.put("printsNumberOld", textList1.get(0).getPrintsNumberOld());//申请时的用印枚数
                    }
                    break;

                //合同关闭审批单
                case "contract_close_main":
                    BmContractClose bmContractClose = bmContractCloseService.getById(id);
                    Json.put("id", bmContractClose.getId());
                    Json.put("contractName", bmContractClose.getContractName());//合同名称
                    Json.put("approvalCode", bmContractClose.getApprovalCode());//审批单号
                    Json.put("createPsnFullName", bmContractClose.getCreatePsnFullName());//经办人
                    Json.put("createPsnId", bmContractClose.getCreatePsnId());//主数据ID
                    Json.put("createTime", bmContractClose.getCreateTime());//经办时间
                    Json.put("closeType", bmContractClose.getCloseType());//关闭类型
                    Json.put("contractExecutedMoney", bmContractClose.getContractExecutedMoney());//已履行金额（元）
                    Json.put("closingDescription", bmContractClose.getClosingDescription());//关闭说明

                    JSONArray closeList = new JSONArray();
                    if (StringUtils.isNotBlank(bmContractClose.getOtherAttachment())) {
                        closeList.addAll(JSON.parseArray(bmContractClose.getOtherAttachment()));
                    }
                    Json.put("otherAttachment", closeList);//附件资料
                    break;

                //合同解除审批单
                case "contract_secure_main":
                    BmContractClose bmContractSecure = bmContractCloseService.getById(id);
                    Json.put("id", bmContractSecure.getId());
                    Json.put("contractName", bmContractSecure.getContractName());//合同名称
                    Json.put("approvalCode", bmContractSecure.getApprovalCode());//审批单号
                    Json.put("createPsnFullName", bmContractSecure.getCreatePsnFullName());//经办人
                    Json.put("createPsnId", bmContractSecure.getCreatePsnId());//主数据ID
                    Json.put("createTime", bmContractSecure.getCreateTime());//经办时间
                    Json.put("closeType", bmContractSecure.getCloseType());//关闭类型
//                    Json.put("contractExecutedMoney", bmContractSecure.getContractExecutedMoney());//已履行金额（元）
                    Json.put("closingDescription", bmContractSecure.getClosingDescription());//解除说明

                    JSONArray secureList = new JSONArray();
                    if (StringUtils.isNotBlank(bmContractSecure.getOtherAttachment())) {
                        secureList.addAll(JSON.parseArray(bmContractSecure.getOtherAttachment()));
                    }
                    Json.put("otherAttachment", secureList);//附件资料
                    break;
                //合规审查
                case "hgsc_main":
                case "hgsc_main_qsss":
                case "hgsc_main_zdhf":
                case "hgsc_main_tsjy":
                case "hgsc_main_hfhg":
                    ComplianceReview complianceReview = complianceReviewService.getById(id);
                    Json.put("id", complianceReview.getId());
                    Json.put("reviewCategoryName", complianceReview.getReviewCategoryName());//标题字段
                    Json.put("reviewSubject", complianceReview.getReviewSubject());//审查主题
                    Json.put("reviewNumber", complianceReview.getReviewNumber());//审查编号
                    if ("内部规章制度合法合规审查".equals(complianceReview.getReviewCategoryName()) || "制度合法合规审查".equals(complianceReview.getReviewCategoryName())) {
                        Json.put("businessArea", complianceReview.getBusinessArea());//业务领域
                        Json.put("requiresTripleOneMeeting", "1".equals(complianceReview.getRequiresTripleOneMeeting()) ? "是" : "否");//是否提交重大决策
                        Json.put("matterDescription", complianceReview.getMatterDescription());//事项描述
                        Json.put("institutionalType", complianceReview.getInstitutionalType());//制度类型
                        JSONArray reviewList = new JSONArray();
                        if (StringUtils.isNotBlank(complianceReview.getReviewMaterials())) {
                            reviewList.addAll(JSON.parseArray(complianceReview.getReviewMaterials()));
                        }
                        Json.put("reviewMaterials", reviewList);//请示内容及相关附件
                        JSONArray basisList = new JSONArray();
                        if (StringUtils.isNotBlank(complianceReview.getBasisForSystemFormulation())) {
                            basisList.addAll(JSON.parseArray(complianceReview.getBasisForSystemFormulation()));
                        }
                        Json.put("basisForSystemFormulation", basisList);//制度制定依据
                        List<ComplianceReviewFixedItem> checkList = complianceReviewFixedItemService.list(new QueryWrapper<ComplianceReviewFixedItem>().eq("parent_id", id));
                        Json.put("checkList", checkList);//请示内容及相关附件
                    } else if ("合法合规审查意见".equals(complianceReview.getReviewCategoryName())) {
                        Json.put("submittingEntityName", complianceReview.getSubmittingEntityName());//论证分类
                        Json.put("requirementDocumentName", complianceReview.getRequirementDocumentName());//拟开展业务模式
                        Json.put("businessArea", complianceReview.getBusinessArea());//业务领域
                        Json.put("matterDescription", complianceReview.getMatterDescription());//事项描述
                        JSONArray reviewList = new JSONArray();
                        if (StringUtils.isNotBlank(complianceReview.getReviewMaterials())) {
                            reviewList.addAll(JSON.parseArray(complianceReview.getReviewMaterials()));
                        }
                        Json.put("reviewMaterials", reviewList);//请示内容及相关附件

                    } else if ("特殊经营类合规论证".equals(complianceReview.getReviewCategoryName())) {
                        Json.put("argumentClassificationName", complianceReview.getArgumentClassificationName());//论证分类
                        Json.put("proposedBusinessModel", complianceReview.getProposedBusinessModel());//拟开展业务模式
                        Json.put("businessArea", complianceReview.getBusinessArea());//业务领域
                        Json.put("matterDescription", complianceReview.getMatterDescription());//事项描述
                        Json.put("otherArgumentClassification", complianceReview.getOtherArgumentClassification());//风险描述
                        JSONArray reviewList = new JSONArray();
                        if (StringUtils.isNotBlank(complianceReview.getReviewMaterials())) {
                            reviewList.addAll(JSON.parseArray(complianceReview.getReviewMaterials()));
                        }
                        Json.put("reviewMaterials", reviewList);//请示内容及相关附件

                        JSONArray agrList = new JSONArray();
                        if (StringUtils.isNotBlank(complianceReview.getArgumentationReport())) {
                            agrList.addAll(JSON.parseArray(complianceReview.getArgumentationReport()));
                        }
                        Json.put("argumentationReport", agrList);//论证报告

                        JSONArray reportList = new JSONArray();
                        if (StringUtils.isNotBlank(complianceReview.getReportFiles())) {
                            reportList.addAll(JSON.parseArray(complianceReview.getReportFiles()));
                        }
                        Json.put("reportFiles", reportList);//事项描述
                    } else if ("重要请示事项合规审查".equals(complianceReview.getReviewCategoryName()) || "请示事项合规审查".equals(complianceReview.getReviewCategoryName())) {
                        Json.put("businessArea", complianceReview.getBusinessArea());//业务领域
                        Json.put("matterDescription", complianceReview.getMatterDescription());//事项描述
                        JSONArray reviewList = new JSONArray();
                        if (StringUtils.isNotBlank(complianceReview.getReviewMaterials())) {
                            reviewList.addAll(JSON.parseArray(complianceReview.getReviewMaterials()));
                        }
                        Json.put("reviewMaterials", reviewList);//请示内容及相关附件
                        JSONArray requestText = new JSONArray();
                        if (StringUtils.isNotBlank(complianceReview.getRequestText())) {
                            requestText.addAll(JSON.parseArray(complianceReview.getRequestText()));
                        }
                        Json.put("requestText", requestText);//请示内容及相关附件
                        List<ComplianceExamination> complianceExaminations = complianceExaminationService.list(new QueryWrapper<ComplianceExamination>().eq("parent_id", id).orderByAsc("sort"));
                        Json.put("examinationLists", complianceExaminations);
                    } else if ("重大决策事项合规审查".equals(complianceReview.getReviewCategoryName())) {
                        Json.put("isComplianceReviewed", "1".equals(complianceReview.getIsComplianceReviewed()) ? "是" : "否");//是否已合规审查
                        Json.put("originalComplianceReview", complianceReview.getOriginalComplianceReview());//原审查主题
                        Json.put("hasProposalChanged", "1".equals(complianceReview.getHasProposalChanged()) ? "是" : "否");//议案是否有变化
                        Json.put("businessArea", complianceReview.getBusinessArea());//业务领域
                        Json.put("matterDescription", complianceReview.getMatterDescription());//事项描述
                        Json.put("decisionContent", complianceReview.getDecisionContent());//决策内容
                        List<ComplianceExamination> complianceExaminations = complianceExaminationService.list(new QueryWrapper<ComplianceExamination>().eq("parent_id", id).orderByAsc("sort"));
                        Json.put("examinationLists", complianceExaminations);
                    }
                    break;
                //投资项目风险审批单
                case "tzxmfx_main":
//                    BmContractClose bmContractSecure = bmContractCloseService.getById(id);
                    InvestmentProjectRiskReview projectRiskReview = investmentProjectRiskReviewService.getById(id);
                    Json.put("id", projectRiskReview.getId());
                    Json.put("createPsnFullName", projectRiskReview.getCreatePsnFullName());//经办人
                    Json.put("createPsnId", projectRiskReview.getCreatePsnId());//经办人主数据ID
                    Json.put("createTime", projectRiskReview.getCreateTime());//经办时间
                    Json.put("relatedInvestment", projectRiskReview.getRelatedInvestment());//关联投资项目风险评估
                    Json.put("projectName", projectRiskReview.getProjectName());//项目名称
                    Json.put("projectNumber", projectRiskReview.getProjectNumber());//风评编号
                    Json.put("projectAmount", projectRiskReview.getProjectAmount());//项目金额（元）
                    Json.put("businessDomain", projectRiskReview.getBusinessDomain());//业务领域
                    Json.put("projectOverview", projectRiskReview.getProjectOverview());//项目概述
                    Json.put("projectType", projectRiskReview.getProjectType());//项目类型
//                    todo liwenhua
                    JSONArray riskAssessmentReportList = new JSONArray();
                    if (StringUtils.isNotBlank(projectRiskReview.getRiskAssessmentReport())) {
                        riskAssessmentReportList.addAll(JSON.parseArray(projectRiskReview.getRiskAssessmentReport()));
                    }
                    Json.put("riskAssessmentReport", riskAssessmentReportList);//风险评估报告
                    JSONArray relevantMaterialsList = new JSONArray();
                    if (StringUtils.isNotBlank(projectRiskReview.getRelevantMaterials())) {
                        relevantMaterialsList.addAll(JSON.parseArray(projectRiskReview.getRelevantMaterials()));
                    }
                    Json.put("relevantMaterials", relevantMaterialsList);//相关资料
                    break;
                //合规过程审批单
                case "fxgcsp_main":
                    ComplianceProcessEntity complianceProcessEntity = complianceProcessService.getById(id);
                    Json.put("id", complianceProcessEntity.getId());
                    Json.put("createPsnFullName", complianceProcessEntity.getCreatePsnFullName());//经办人
                    Json.put("createPsnId", complianceProcessEntity.getCreatePsnId());//经办人主数据ID
                    Json.put("createTime", complianceProcessEntity.getCreateTime());//经办时间
                    Json.put("itemName", complianceProcessEntity.getItemName());//事项名称
                    Json.put("itemNumber", complianceProcessEntity.getItemNumber());//事项编码
                    Json.put("relatedItem", complianceProcessEntity.getRelatedItem());//关联事项
                    Json.put("itemCategory", complianceProcessEntity.getItemCategory());//事项分类
                    JSONArray relatedAttachmentList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceProcessEntity.getRelatedAttachment())) {
                        relatedAttachmentList.addAll(JSON.parseArray(complianceProcessEntity.getRelatedAttachment()));
                    }
                    Json.put("plannedStartCheckTime", complianceProcessEntity.getPlannedStartCheckTime());//计划开始检查时间
                    Json.put("plannedEndCheckTime", complianceProcessEntity.getPlannedEndCheckTime());//计划结束检查时间
                    Json.put("participatingDepartments", complianceProcessEntity.getParticipatingDepartments());//参与部门
                    Json.put("checkNotification", complianceProcessEntity.getCheckNotification());//检查通知
                    JSONArray notificationAttachmentList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceProcessEntity.getNotificationAttachment())) {
                        notificationAttachmentList.addAll(JSON.parseArray(complianceProcessEntity.getNotificationAttachment()));
                    }
                    Json.put("checkResult", complianceProcessEntity.getCheckResult());//检查结果
                    Json.put("isRectification", complianceProcessEntity.getIsRectification());//是否整改
                    JSONArray relatedAttachmentCheckList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceProcessEntity.getRelatedAttachmentCheck())) {
                        relatedAttachmentCheckList.addAll(JSON.parseArray(complianceProcessEntity.getRelatedAttachmentCheck()));
                    }
                    Json.put("relatedAttachmentCheck", relatedAttachmentCheckList);//相关附件
                    break;

                //风险过程审批
                case "risk_approval_process_main":
                    RiskApprovalProcessEntity riskApprovalProcess = riskApprovalProcessService.getById(id);
                    Json.put("id", riskApprovalProcess.getId());
                    Json.put("createPsnFullName", riskApprovalProcess.getCreatePsnFullName()); // 经办人
                    Json.put("createPsnId", riskApprovalProcess.getCreatePsnId()); // 主数据ID
                    Json.put("createTime", riskApprovalProcess.getCreateTime()); // 经办时间
                    Json.put("associatedRisks", riskApprovalProcess.getAssociatedRisks()); // 关联风险
                    Json.put("processNumber", riskApprovalProcess.getProcessNumber()); // 风险编号
                    Json.put("approvalNumber", riskApprovalProcess.getApprovalNumber()); // 审批编号
                    Json.put("eventNature", riskApprovalProcess.getEventNature()); // 事件性质
                    Json.put("riskLevel", riskApprovalProcess.getExpectedRiskLevel()); // 风险等级
                    Json.put("businessField", riskApprovalProcess.getBusinessField()); // 业务领域
                    Json.put("involvedAmount", riskApprovalProcess.getInvolvedAmount()); // 涉及金额(元)
                    Json.put("riskSource", riskApprovalProcess.getRiskSource()); // 风险源
                    Json.put("acceptingInstitution", riskApprovalProcess.getAcceptingInstitution()); // 监管部门
                    Json.put("itemDescription", riskApprovalProcess.getItemDescription()); // 风险描述
                    Json.put("riskReason", riskApprovalProcess.getRiskReason()); // 风险原因
                    // Json.put("relatedAttachment", riskApprovalProcess.getRelatedAttachment()); // 关联资料

                    JSONArray riskApprovalProcessList = new JSONArray();
                    if (StringUtils.isNotBlank(riskApprovalProcess.getRelatedAttachment())) {
                        riskApprovalProcessList.addAll(JSON.parseArray(riskApprovalProcess.getRelatedAttachment()));
                    }
                    Json.put("relatedAttachment", riskApprovalProcessList);//附件资料
                    break;

                //合规报告审批
                case "hgbg_main":
                    ComplianceReportEntity complianceReport = complianceReportService.getById(id);
                    Json.put("id", complianceReport.getId());
                    Json.put("createPsnFullName", complianceReport.getCreatePsnFullName()); // 经办人
                    Json.put("createPsnId", complianceReport.getCreatePsnId()); // 主数据ID
                    Json.put("createTime", complianceReport.getCreateTime()); // 经办时间
                    Json.put("reportSubject", complianceReport.getReportSubject()); // 报告主题
                    Json.put("processNumber", complianceReport.getProcessNumber()); // 报告编码
                    Json.put("reportCategory", complianceReport.getReportCategory()); // 报告类别
                    Json.put("reportYear", complianceReport.getReportYear()); // 报告年度
                    // Json.put("reportFile", complianceReport.getReportFile()); // 报告文件

                    JSONArray complianceReportList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceReport.getReportFile())) {
                        complianceReportList.addAll(JSON.parseArray(complianceReport.getReportFile()));
                    }
                    Json.put("relatedAttachment", complianceReportList);//附件资料
                    break;

                //风险事件上报审批
                case "Risk_Waring_Main_Approval":
                    ComplianceRiskWarning complianceRisk = complianceRiskWaringService.getById(id);
                    if (complianceRisk != null) {
                        List<ComplianceRiskMitigation> complianceRiskMitigations = complianceRiskMitigationService.queryByParnetId(id);
                        complianceRisk.setMitigationList(complianceRiskMitigations);
                    }
                    Json.put("id", complianceRisk.getId());
                    Json.put("createPsnFullName", complianceRisk.getCreatePsnFullName()); // 经办人
                    Json.put("createPsnId", complianceRisk.getCreatePsnId()); // 主数据ID
                    Json.put("createTime", complianceRisk.getCreateTime()); // 经办时间
                    Json.put("associatedRiskWarning", complianceRisk.getAssociatedRiskWarning()); // 关联风险预警
                    Json.put("riskNumber", complianceRisk.getRiskNumber()); // 风险编号
                    Json.put("riskName", complianceRisk.getRiskName()); // 风险名称
                    Json.put("eventNature", complianceRisk.getEventNature()); // 事件性质
                    Json.put("riskClassification", complianceRisk.getRiskClassification()); // 风险类别
                    Json.put("expectedRiskLevelName", complianceRisk.getExpectedRiskLevelName()); // 风险等级
                    Json.put("businessDomainName", complianceRisk.getBusinessDomainName()); // 业务领域
                    Json.put("involvedAmount", complianceRisk.getInvolvedAmount()); // 涉及金额(元）
                    Json.put("riskDescription", complianceRisk.getRiskDescription()); // 风险描述
                    Json.put("riskReason", complianceRisk.getRiskReason()); // 风险原因
                    Json.put("receivingAgency", complianceRisk.getReceivingAgency()); // 监管部门
//                    Json.put("uploadAttachment", complianceRisk.getUploadAttachment()); // 相关文书
                    Json.put("riskType", complianceRisk.getRiskType()); // 风险类型
                    String riskMitigationStatusName = "";
                    if (complianceRisk.getRiskMitigationStatus().equals("2")) {
                        riskMitigationStatusName = "未应对";
                    } else if (complianceRisk.getRiskMitigationStatus().equals("3")) {
                        riskMitigationStatusName = "应对中";
                    } else if (complianceRisk.getRiskMitigationStatus().equals("4")) {
                        riskMitigationStatusName = "已完成";
                    }
                    Json.put("riskMitigationStatusName", riskMitigationStatusName); // 应对状态
                    JSONArray respondProcessFilesList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceRisk.getRespondProcessFiles())) {
                        respondProcessFilesList.addAll(JSON.parseArray(complianceRisk.getRespondProcessFiles()));
                    }
                    Json.put("respondProcessFiles", respondProcessFilesList);//应对过程文件
                    JSONArray legalObligationsList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceRisk.getLegalObligations())) {
                        legalObligationsList.addAll(JSON.parseArray(complianceRisk.getLegalObligations()));
                    }
                    Json.put("legalObligations", legalObligationsList);//涉及法律义务条款及相关法律责任条款

                    JSONArray summaryEventList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceRisk.getSummaryEvent())) {
                        summaryEventList.addAll(JSON.parseArray(complianceRisk.getSummaryEvent()));
                    }
                    Json.put("summaryEvent", summaryEventList);//事件总结
                    Json.put("improvementMeasures", complianceRisk.getImprovementMeasures());//改进措施
                    JSONArray resultCredentialList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceRisk.getResultCredential())) {
                        resultCredentialList.addAll(JSON.parseArray(complianceRisk.getResultCredential()));
                    }
                    Json.put("resultCredential", resultCredentialList);//结果凭证
                    JSONArray summaryFileList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceRisk.getSummaryFile())) {
                        summaryFileList.addAll(JSON.parseArray(complianceRisk.getSummaryFile()));
                    }
                    Json.put("summaryFile", summaryFileList);//关联资料

                    JSONArray mitigationLists = new JSONArray();
                    List<?> mitigationList = complianceRisk.getMitigationList();
                    if (mitigationList != null && !mitigationList.isEmpty()) {
                        mitigationLists.addAll(JSON.parseArray(JSON.toJSONString(mitigationList)));
                    }
                    Json.put("mitigationList", mitigationLists); // 应对过程


                    JSONArray complianceRiskList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceRisk.getUploadAttachment())) {
                        complianceRiskList.addAll(JSON.parseArray(complianceRisk.getUploadAttachment()));
                    }
                    Json.put("uploadAttachment", complianceRiskList);//相关文书
                    break;
                //协同监督审批单
                case "xtjd_main":
                    ComplianceAccountability complianceAccountability = complianceAccountabilityService.getById(id);
                    Json.put("id", complianceAccountability.getId());
                    Json.put("createPsnFullName", complianceAccountability.getCreatePsnFullName());//经办人
                    Json.put("createPsnId", complianceAccountability.getCreatePsnId());//经办人主数据ID
                    Json.put("createTime", complianceAccountability.getCreateTime());//经办时间
                    Json.put("accountabilitySubject", complianceAccountability.getAccountabilitySubject());//主题
                    Json.put("code", complianceAccountability.getCode());//编号
                    Json.put("accountabilityTime", complianceAccountability.getAccountabilityTime());//时间
                    Json.put("responsibleParty", complianceAccountability.getResponsibleParty());//对象
                    Json.put("oaDept", complianceAccountability.getOaDept());//协同单位
                    Json.put("relatedMatter", complianceAccountability.getRelatedMatter());//关联事件
                    Json.put("violationMatter", complianceAccountability.getViolationMatter());//违规事件
                    JSONArray uploadAttachmentList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceAccountability.getUploadAttachment())) {
                        uploadAttachmentList.addAll(JSON.parseArray(complianceAccountability.getUploadAttachment()));
                    }
                    Json.put("uploadAttachment", uploadAttachmentList);//上传附件
                    break;
                //合规问责审批单
                case "hgwz_main":
                    ComplianceAccountability complianceAccountabilityHgwz = complianceAccountabilityService.getById(id);
                    Json.put("id", complianceAccountabilityHgwz.getId());
                    Json.put("createPsnFullName", complianceAccountabilityHgwz.getCreatePsnFullName());//经办人
                    Json.put("createPsnId", complianceAccountabilityHgwz.getCreatePsnId());//经办人主数据ID
                    Json.put("createTime", complianceAccountabilityHgwz.getCreateTime());//经办时间
                    Json.put("accountabilitySubject", complianceAccountabilityHgwz.getAccountabilitySubject());//主题
                    Json.put("code", complianceAccountabilityHgwz.getCode());//编号
                    Json.put("accountabilityTime", complianceAccountabilityHgwz.getAccountabilityTime());//时间
                    Json.put("responsibleParty", complianceAccountabilityHgwz.getResponsibleParty());//对象
                    Json.put("oaDept", complianceAccountabilityHgwz.getOaDept());//协同单位
                    Json.put("relatedMatter", complianceAccountabilityHgwz.getRelatedMatter());//关联事件
                    Json.put("violationMatter", complianceAccountabilityHgwz.getViolationMatter());//违规事件
                    JSONArray uploadAttachmentHgwzList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceAccountabilityHgwz.getUploadAttachment())) {
                        uploadAttachmentHgwzList.addAll(JSON.parseArray(complianceAccountabilityHgwz.getUploadAttachment()));
                    }
                    Json.put("uploadAttachment", uploadAttachmentHgwzList);//上传附件
                    break;
                //合规检查结果审批流程
                case "check_result_main":
                    ComplianceChackEntity complianceChackEntity = complianceChackService.queryById(id);
                    Json.put("id", complianceChackEntity.getId());
                    Json.put("createPsnFullName", complianceChackEntity.getCreatePsnFullName());//经办人
                    Json.put("createPsnId", complianceChackEntity.getCreatePsnId());//经办人主数据ID
                    Json.put("createTime", complianceChackEntity.getCreateTime());//经办时间
                    Json.put("itemName", complianceChackEntity.getChackInfo().getItemName());//检查名称
                    Json.put("itemCode", complianceChackEntity.getChackInfo().getItemCode());//事项编码
                    Json.put("plannedStartTime", complianceChackEntity.getChackInfo().getPlannedStartTime());//计划开始检查时间
                    Json.put("plannedEndTime", complianceChackEntity.getChackInfo().getPlannedEndTime());//计划结束检查时间
                    Json.put("inspectedUnitName", complianceChackEntity.getChackInfo().getInspectedUnitName());//被检查单位
                    Json.put("involvedDepartmentList", complianceChackEntity.getChackInfo().getInvolvedDepartmentList());//参与部门
                    Json.put("inspectionNotice", complianceChackEntity.getChackInfo().getInspectionNotice());//检查通知
                    JSONArray relatedAttachmentsCreaterList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceChackEntity.getChackInfo().getRelatedAttachmentsCreater())) {
                        relatedAttachmentsCreaterList.addAll(JSON.parseArray(complianceChackEntity.getChackInfo().getRelatedAttachmentsCreater()));
                    }
                    Json.put("relatedAttachmentsCreater", relatedAttachmentsCreaterList);//相关附件
                    Json.put("inspectionResult", complianceChackEntity.getReviewResult().getInspectionResult());//检查结果
                    Json.put("needsRectification", complianceChackEntity.getReviewResult().getNeedsRectification());//是否整改
                    Json.put("latestRectificationTime", complianceChackEntity.getReviewResult().getLatestRectificationTime());//整改完成时间
                    JSONArray relatedAttachmentsResultList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceChackEntity.getReviewResult().getRelatedAttachmentsResult())) {
                        relatedAttachmentsResultList.addAll(JSON.parseArray(complianceChackEntity.getReviewResult().getRelatedAttachmentsResult()));
                    }
                    Json.put("relatedAttachmentsResult", complianceChackEntity.getReviewResult().getRelatedAttachmentsResult());//相关附件
                    break;
                //合规专项检查审批流程
                case "hgzxjh_main":
                case "hgyxxpj_main":
                    ComplianceChackEntity complianceChackResultEntity = complianceChackService.queryById(id);
                    Json.put("id", complianceChackResultEntity.getId());
                    Json.put("createPsnFullName", complianceChackResultEntity.getCreatePsnFullName());//经办人
                    Json.put("createPsnId", complianceChackResultEntity.getCreatePsnId());//经办人主数据ID
                    Json.put("createTime", complianceChackResultEntity.getCreateTime());//经办时间
                    Json.put("itemName", complianceChackResultEntity.getChackInfo().getItemName());//检查名称
                    Json.put("itemCode", complianceChackResultEntity.getChackInfo().getItemCode());//事项编码
                    Json.put("plannedStartTime", complianceChackResultEntity.getChackInfo().getPlannedStartTime());//计划开始检查时间
                    Json.put("plannedEndTime", complianceChackResultEntity.getChackInfo().getPlannedEndTime());//计划结束检查时间
                    Json.put("inspectedUnitName", complianceChackResultEntity.getChackInfo().getInspectedUnitName());//被检查单位
                    Json.put("involvedDepartmentList", complianceChackResultEntity.getChackInfo().getInvolvedDepartmentList());//参与部门
                    Json.put("inspectionNotice", complianceChackResultEntity.getChackInfo().getInspectionNotice());//检查通知
                    JSONArray relatedAttachmentsCreaterResultList = new JSONArray();
                    if (StringUtils.isNotBlank(complianceChackResultEntity.getChackInfo().getRelatedAttachmentsCreater())) {
                        relatedAttachmentsCreaterResultList.addAll(JSON.parseArray(complianceChackResultEntity.getChackInfo().getRelatedAttachmentsCreater()));
                    }
                    Json.put("relatedAttachmentsCreater", relatedAttachmentsCreaterResultList);//相关附件
                    break;
                //重大项目初步论证
                case "project_demonstration_main":
                    ProjectDemonstration projectDemonstration = projectDemonstrationService.getById(id);
                    Json.put("id", projectDemonstration.getId());
                    //项目名称
                    Json.put("projectName", projectDemonstration.getProjectName());
                    //项目编码
                    Json.put("projectNumber", projectDemonstration.getProjectNumber());
                    //涉及金额
                    Json.put("involvedAmount", projectDemonstration.getInvolvedAmount());
                    //项目背景
                    Json.put("projectBackground", projectDemonstration.getProjectBackground());
                    //相关方情况
                    Json.put("relatedPartySituation", projectDemonstration.getRelatedPartySituation());
                    //初步思路
                    Json.put("preliminaryIdeas", projectDemonstration.getPreliminaryIdeas());
                    //论证关注重点
                    Json.put("keyFocusOfArgumentation", projectDemonstration.getKeyFocusOfArgumentation());
                    //存在的问题
                    Json.put("existsProblem", projectDemonstration.getExistsProblem());
                    break;
                //重大项目论证
                case "Project_Major_Reasoning_App":
                    ProjectMajorReasoning projectMajorReasoning = projectMajorReasoningService.getById(id);
                    //项目任务
                    List<ProjectMajorDetail> projectMajorDetails = projectMajorDetailService.queryByParentId(id);
                    Json.put("id", projectMajorReasoning.getId());
                    //项目名称
                    Json.put("projectName", projectMajorReasoning.getProbjectName());
                    //项目编码
                    Json.put("projectNumber", projectMajorReasoning.getProbjectNumber());
                    //涉及金额
                    Json.put("involvedAmount", projectMajorReasoning.getInvolvedAmount());
                    //项目背景
                    Json.put("projectBackground", projectMajorReasoning.getProbjectBackground());
                    //相关方情况
                    Json.put("relatedPartySituation", projectMajorReasoning.getRelatedPartySituation());
                    //初步思路
                    Json.put("preliminaryIdeas", projectMajorReasoning.getPreliminaryIdeas());
                    //论证关注重点
                    Json.put("keyFocusOfArgumentation", projectMajorReasoning.getKeyFocusOfArgumentation());
                    //存在的问题
                    Json.put("existsProblem", projectMajorReasoning.getExistsProblem());
                    //关联初步论证
                    Json.put("associationPreliminaryContext", projectMajorReasoning.getAssociationPreliminaryContext());
                    //项目任务List封装
                    JSONArray jsonArray = new JSONArray();
                    if (!projectMajorDetails.isEmpty()) {
                        projectMajorDetails.forEach(pro -> {
                            JSONObject json = new JSONObject();
                            json.put("taskNumber", pro.getTaskNumber());
                            json.put("jobContext", pro.getJobContext());
                            json.put("planStartTime", DateUtil.format(pro.getPlanEndTime(), "yyyy-MM-dd"));
                            json.put("planEndTime", DateUtil.format(pro.getPlanEndTime(), "yyyy-MM-dd"));
                            json.put("deptName", pro.getDeptName());
                            json.put("registrationTime", pro.getRegistrationTime());
                            json.put("createPsnName", pro.getCreatePsnName());
                            jsonArray.add(json);
                        });
                    }
                    Json.put("taskList", jsonArray.toJSONString());
                    break;
                //重大项目论证总结
                case "Project_Major_Summary_App":
                    ProjectMajorSummary projectMajorSummary = projectMajorSummaryService.getById(id);
                    Json.put("id", projectMajorSummary.getId());
                    //关联初步论证
                    Json.put("pelatedProjectArgumentation", projectMajorSummary.getPelatedProjectArgumentation());
                    //项目名称
                    Json.put("projectName", projectMajorSummary.getProjectName());
                    //项目编码
                    Json.put("projectNumber", projectMajorSummary.getProjectNumber());
                    //涉及金额
                    Json.put("involvedAmount", projectMajorSummary.getInvolvedAmount());
                    //项目背景
                    Json.put("projectSituation", projectMajorSummary.getProjectSituation());
                    //项目基本情况
                    Json.put("relatedPartySituation", projectMajorSummary.getRiskWarningResMeasures());
                    //必要性
                    Json.put("necessity", projectMajorSummary.getNecessity());
                    //论证情况
                    Json.put("argumentSituation", projectMajorSummary.getArgumentSituation());
                    //重要约定事项
                    Json.put("importantAgreements", projectMajorSummary.getImportantAgreements());
                    //重要风险提示及应对措施
                    Json.put("existsProblem", projectMajorSummary.getRiskWarningResMeasures());
                    //其他事项
                    Json.put("otherMatters", projectMajorSummary.getOtherMatters());
                    //项目论证总结
                    Json.put("summaryFilePath", projectMajorSummary.getSummaryFilePath());
                    break;

                default:
                    Json.put("data", "未找到业务功能编码");
                    log.info("未找到业务功能编码");
            }
            return Json;
        }
        return Json;
    }

    private void getParse(JSONObject json, UserTaskExt oneUserTaskExt1) {
        for (customPropertiesDto customProperty : oneUserTaskExt1.getCustomProperties()){
            if("FlowPropertiesSettingOfRevenueServiceImpl".equals(customProperty.getKey())){
                JSONObject parseObject = JSONObject.parseObject((String) customProperty.getValue());
                if(parseObject != null){
                    boolean equals = "是".equals(parseObject.getString("name"));
                    json.put("parseCompliance", equals);//是否合规节点
                }
            }
        }
    }

    public JSONObject saveData(JSONObject jsonObject) {
        JSONObject Json = new JSONObject();
        String documentType = jsonObject.getString("documentType");
        String id = jsonObject.getString("id");
        if (StringUtils.isBlank(documentType) || StringUtils.isBlank(id)) {
            Json.put("Error", "documentType或id不存在");
            return Json;
        }
        String businessLeaders = jsonObject.getString("businessLeaders");//业务负责人考核意见
        String departmentHeads = jsonObject.getString("departmentHeads");//部门负责人考核意见
        BigDecimal assessmentScore = jsonObject.getBigDecimal("assessmentScore");//考核分数
        switch (documentType) {
            case "contract_approval_main":
            case "contract_change_main":
                BmContract bmContract = bmContractService.getById(id);
                bmContract.setBusinessLeaders(businessLeaders);
                bmContract.setDepartmentHeads(departmentHeads);
                bmContract.setAssessmentScore(assessmentScore);
                bmContractService.updateById(bmContract);
                break;
            //合同合并
            case "contract_more_main":
                BmContractBatch bmContractBatch = bmContractBatchService.getById(id);
                bmContractBatch.setBusinessLeaders(businessLeaders);
                bmContractBatch.setDepartmentHeads(departmentHeads);
                bmContractBatch.setAssessmentScore(assessmentScore);
                bmContractBatchService.updateById(bmContractBatch);
                break;
            case "case_risks_main":
                BmCaseRisk bmCaseRisk = bmCaseRiskService.getById(id);
                bmCaseRisk.setBusinessLeaders(businessLeaders);
                bmCaseRisk.setDepartmentHeads(departmentHeads);
                bmCaseRisk.setAssessmentScore(assessmentScore);
                bmCaseRiskService.updateById(bmCaseRisk);
                break;
        }
        return Json;

    }

    public JSONObject saveRiskData(JSONObject jsonObject) {
        JSONObject Json = new JSONObject();
        String documentType = jsonObject.getString("documentType");
        String id = jsonObject.getString("id");
        if (StringUtils.isBlank(documentType) || StringUtils.isBlank(id)) {
            Json.put("Error", "documentType或id不存在");
            return Json;
        }
        String riskPsn = jsonObject.getString("riskPsn");//风控清单附件上传人
        String riskPsnCode = jsonObject.getString("riskPsnCode");//风控清单附件上传人
        Date undertakingTime = jsonObject.getDate("undertakingTime");
        String complianceFiles = jsonObject.getString("complianceFiles");//风控清单
        switch (documentType) {
            case "contract_approval_main":
            case "contract_change_main":
                BmContract bmContract = bmContractService.getById(id);
                if (StringUtils.isBlank(bmContract.getRiskPsn())) {
                    bmContract.setRiskPsn(riskPsn);
                    bmContract.setRiskPsnCode(riskPsnCode);
                    bmContract.setUndertakingTime(new Date());
                }
                bmContract.setComplianceFiles(complianceFiles);
                bmContractService.updateById(bmContract);
                break;
            //合同合并
            case "contract_more_main":
                BmContractBatch bmContractBatch = bmContractBatchService.getById(id);
                if (StringUtils.isBlank(bmContractBatch.getRiskPsn())) {
                    bmContractBatch.setRiskPsn(riskPsn);
                    bmContractBatch.setRiskPsnCode(riskPsnCode);
                    bmContractBatch.setUndertakingTime(new Date());
                }
                bmContractBatch.setComplianceFiles(complianceFiles);
                bmContractBatchService.updateById(bmContractBatch);
                break;
        }
        return Json;

    }
}
