<template>
  <el-card class="container" header="删除待办">
    <div class="input-group">
      <el-form label-width="80px">
        <el-form-item label="流程ID：">
          <el-input
            v-model="processInstanceId"
            placeholder="请输入流程ID "
          ></el-input>
        </el-form-item>

        <el-form-item label="用户ID：">
          <el-input
            v-model="userId"
            placeholder="请输入用户ID"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitRequest">提交请求</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card v-if="response" class="response-box">
      <h3>返回结果：</h3>
      <pre>{{ formattedResponse }}</pre>
    </el-card>
  </el-card>
</template>

<script>

import deleteTaskApi from '@/api/yunwei/deleteTask'

export default {
  data() {
    return {
      processInstanceId: '',
      userId: '',
      response: null
    }
  },
  computed: {
    formattedResponse() {
      return JSON.stringify(this.response, null, 2)
    }
  },
  methods: {
    async submitRequest() {
      if (!this.processInstanceId || !this.userId) {
        this.$message.error('请填写完整信息')
        return
      }

      try {
        const requestData = {
          syscode: "jtgk_fw",
          flowid: this.processInstanceId,
          userid: this.userId
        };

        const response = await deleteTaskApi.deleteUserRequestInfo(requestData);
        this.response = response.data;
        // console.log(response);
      } catch (error) {
        console.log(error);
        this.response = {
          error: '请求失败',
          details: error.message
        };
      }
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 600px;
  margin: 20px auto;
  padding: 20px;
}

.input-group {
  margin-bottom: 15px;
}

.response-box {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fafafa;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>