package com.klaw.service;

import com.alibaba.fastjson.JSONObject;
import com.klaw.service.complianceReviewService.ComplianceReviewService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * 合规审查服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class ComplianceReviewServiceTest {

    @Resource
    private ComplianceReviewService complianceReviewService;

    @Test
    public void testQueryPageData() {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("page", 1);
            jsonObject.put("limit", 10);
            jsonObject.put("isQuery", true);

            // 测试查询方法是否正常工作
            var result = complianceReviewService.queryPageData(jsonObject);
            System.out.println("查询成功，结果数量: " + result.getRecords().size());

            // 打印一些数据用于验证
            if (!result.getRecords().isEmpty()) {
                var first = result.getRecords().get(0);
                System.out.println("第一条数据:");
                System.out.println("  事项题目: " + first.getReviewSubject());
                System.out.println("  审查分类: " + first.getReviewCategoryName());
                System.out.println("  业务领域: " + first.getBusinessArea());
                System.out.println("  送审人: " + first.getSubmitter());
                System.out.println("  送审单位: " + first.getSubmittingUnit());
                System.out.println("  送审时间: " + first.getSubmissionDate());
                System.out.println("  审查状态: " + first.getDataState());
            }
        } catch (Exception e) {
            System.err.println("查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
