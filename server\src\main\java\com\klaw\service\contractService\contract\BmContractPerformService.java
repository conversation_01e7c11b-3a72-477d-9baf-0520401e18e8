package com.klaw.service.contractService.contract;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.klaw.entity.contractBean.ContractWarn;
import com.klaw.entity.contractBean.contract.BmContract;
import com.klaw.entity.contractBean.contract.BmContractPerform;
import com.klaw.entity.contractBean.contract.vo.BmContractPerformVO;
import com.klaw.utils.PageUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-14
 */
public interface BmContractPerformService extends IService<BmContractPerform> {

    /**
     * BmContractPerform 详情
     *
     * @param
     * @return
     */
    BmContractPerform getBmContractPerform(String id);
    BmContractPerform getBmContractPerformByPerformCode(String performCode);
    List<BmContractPerform> queryByContractCode(String contractCode);

    /**
     * BmContractPerform 新增
     *
     * @param bmContractPerform 根据需要进行传值
     * @return
     */
    int saveData(BmContractPerform bmContractPerform);

    /**
     * BmContractPerform 修改
     *
     * @param bmContractPerform 根据需要进行传值
     * @return
     */
    int modify(BmContractPerform bmContractPerform);

    /**
     * BmContractPerform 删除
     *
     * @param ids
     * @return
     */
    void remove(String ids);


    List<BmContractPerform> queryByContractId(String contractId);
    List<BmContractPerform> queryByIdNotPlanAmount(String contractId);

    void submitPerform(String contractId);

    boolean createScheduler(ContractWarn contractWarn);

    boolean checkPerformStatus(String contractId,Integer state);

    /**
     * 变更履行计划状态
     * @param contractId
     */
    void changePerformStatus(String contractId,String status,String statusDesc);

    PageUtils<BmContractPerformVO> loadPerfomList(JSONObject jsonObject);
    List<BmContractPerformVO> loadPerfomDataList(JSONObject jsonObject);

    String generatePerformDataExcel(List<BmContractPerformVO> dataList,String fileName);

    boolean cancel(String id);
}


