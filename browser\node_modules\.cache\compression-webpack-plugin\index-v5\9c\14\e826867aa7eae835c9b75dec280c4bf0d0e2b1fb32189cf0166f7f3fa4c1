
04837230546b45a940e17d5c05eb4e000f608fa2	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"305.5366aeafafa799d367fa.hot-update.js\",\"contentHash\":\"f780cd0c28ed35176f61c3d88faa09dd\"}","integrity":"sha512-Hl5Ht7kI9bRyoH5pPksXC4n3TpzPqYjERnpISdSdrFYC0vjX2NfMukhx2fjp4x7RAkF/JajCPkPkBItw49WdiQ==","time":1756905072604,"size":59039}