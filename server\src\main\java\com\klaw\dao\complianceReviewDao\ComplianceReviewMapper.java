package com.klaw.dao.complianceReviewDao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.klaw.entity.complianceReviewBean.ComplianceReview;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.klaw.entity.contractBean.contract.BmContract;
import com.klaw.utils.PageUtils;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【compliance_review(合规审查表)】的数据库操作Mapper
 * @createDate 2024-11-26 18:23:57
 * @Entity com.klaw.entity.complianceReviewBean.ComplianceReview
 */
public interface ComplianceReviewMapper extends BaseMapper<ComplianceReview> {
    /**
     * 分页插件
     *
     * @param page
     * @return
     */
    PageUtils<ComplianceReview> queryPageList(Page<ComplianceReview> page, @Param("ew") QueryWrapper<ComplianceReview> queryWrapper);


}




