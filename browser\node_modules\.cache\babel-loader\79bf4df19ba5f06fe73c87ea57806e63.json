{"remainingRequest": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Desktop\\XM\\FW\\baogang\\browser\\src\\view\\Compliance\\Management\\ComplianceOperation\\ComplianceReport\\HgscTz.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\src\\view\\Compliance\\Management\\ComplianceOperation\\ComplianceReport\\HgscTz.vue", "mtime": 1756905062235}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\babel.config.js", "mtime": 1744958013967}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n// 组件\nimport pagination from '@/view/components/Pagination/PaginationIndex';\nimport TableTools from '@/view/components/TableTools/index';\nimport SimpleBoardIndex from '@/view/components/SimpleBoard/SimpleBoardIndex';\nimport orgTree from '@/view/components/OrgTree/OrgTree';\n// vuex审查状态值\nimport { mapGetters } from 'vuex';\n\n// 接口api\nimport complianceReviewApi from '@/api/ComplianceReview/complianceReview.js';\nimport taskApi from '@/api/_system/task';\nexport default {\n  name: 'HgscIndex',\n  inject: ['layout'],\n  components: {\n    pagination: pagination,\n    TableTools: TableTools,\n    SimpleBoardIndex: SimpleBoardIndex,\n    orgTree: orgTree\n  },\n  data: function data() {\n    return {\n      isCheckedUser: false,\n      showUser: false,\n      deptOrgVisible: false,\n      entrustedUnitOrgVisible: false,\n      // is_Check: false,\n      tableQuery: {\n        page: 1,\n        limit: 10,\n        total: 0,\n        reviewSubject: '',\n        // 事项题目\n        createOgnName: '',\n        // 送审单位\n        reviewCategory: '',\n        // 审查分类\n        reviewCategoryName: '',\n        // 审查分类\n        submitter: '',\n        // 送审人\n        submissionDateStart: '',\n        // 送审开始时间\n        submissionDateEnd: '',\n        // 送审结束时间\n        fuzzyValue: '',\n        // 模糊搜索值\n        dataState: '',\n        //台账专用---不等于该值\n        isQuery: true,\n        //是否查询台账\n        orgId: '',\n        // 上报单位id\n        businessArea: '' //业务领域\n      },\n\n      table_height: '100%',\n      tableData: [],\n      userDialogVisible: false,\n      orgDialogVisible: false,\n      orgTreeDialog: false,\n      zxcheckedData: [],\n      orgVisible: false,\n      tableLoading: false,\n      ss: {\n        data: this.tableData,\n        tableColumns: [{\n          key: 'reviewSubject',\n          label: '事项题目',\n          visible: true\n        }, {\n          key: 'reviewCategory',\n          label: '审查分类',\n          visible: true\n        }, {\n          key: 'reviewCategoryName',\n          label: '审查分类',\n          visible: true\n        }, {\n          key: 'submissionDate',\n          label: '送审时间',\n          visible: true\n        }, {\n          key: 'submitter',\n          label: '送审人',\n          visible: true\n        }, {\n          key: 'createOgnName',\n          label: '送审单位',\n          visible: true\n        }, {\n          key: 'dataState',\n          label: '审查状态',\n          visible: true\n        }, {\n          key: 'businessArea',\n          label: '业务领域',\n          visible: true\n        }]\n      },\n      // tableQuery: {\n      //   reportYear: '',\n      // },\n      yearOptions: Array.from({\n        length: 10\n      }, function (_, i) {\n        return new Date().getFullYear() - i;\n      }),\n      businessAreaData: []\n    };\n  },\n  computed: _objectSpread({}, mapGetters(['orgContext', 'currentFunctionId'])),\n  activated: function activated() {\n    // 长连接页面第二次激活的时候,不会走created方法,会走此方法\n    this.refreshData();\n  },\n  created: function created() {\n    this.refreshData();\n    this.initDic();\n  },\n  mounted: function mounted() {\n    this.$nextTick(function () {\n      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45;\n\n      // 监听窗口大小变化\n      var self = this;\n      window.onresize = function () {\n        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45;\n      };\n    });\n  },\n  methods: {\n    initDic: function initDic() {\n      var _this = this;\n      var code = ['businessDomainDic'];\n      this.utils.getDic(code).then(function (response) {\n        _this.businessAreaData = response.data.data[code[0]];\n      });\n    },\n    isEdit: function isEdit(row) {\n      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataStateCode === this.utils.dataState_BPM.BACK.code;\n    },\n    isDelete: function isDelete(row) {\n      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code;\n    },\n    // 刷新数据\n    refreshData: function refreshData() {\n      var _this2 = this;\n      // 赋值当前人组织全路径\n      this.tableQuery.functionCode = this.currentFunctionId.functionCode;\n      this.tableQuery.orgId = this.orgContext.currentOrgId;\n      this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId;\n      // 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页\n      if (this.tableData.length === 0 && this.tableQuery.page > 1) {\n        this.tableQuery.page--;\n      }\n      complianceReviewApi.query(this.tableQuery).then(function (response) {\n        var rows = response.data.data.records;\n        _this2.tableData = rows;\n        _this2.ss.data = rows;\n        _this2.tableQuery.total = response.data.data.total;\n        _this2.tableLoading = false;\n      }).catch({});\n    },\n    // 点击打开查找机构弹窗\n    chooseNoticeDeptClick: function chooseNoticeDeptClick() {\n      this.deptOrgVisible = true;\n    },\n    // 关闭审查机构弹窗\n    deptOrgCancel: function deptOrgCancel() {\n      this.deptOrgVisible = false;\n    },\n    // 点击打开送审人弹窗\n    choiceEntrustedUnitClick: function choiceEntrustedUnitClick() {\n      this.entrustedUnitOrgVisible = true;\n    },\n    // 点击关闭送审人弹窗\n    entrustedUnitOrgCancel: function entrustedUnitOrgCancel() {\n      this.entrustedUnitOrgVisible = false;\n    },\n    entrustedUnitSure: function entrustedUnitSure() {\n      var res = this.zxcheckedData[0];\n      console.log(res, 'res');\n      this.tableQuery.submitter = res.name;\n      this.entrustedUnitOrgVisible = false;\n    },\n    choiceNoticeDeptSure: function choiceNoticeDeptSure() {\n      var c = '';\n      var cid = '';\n      this.zxcheckedData.forEach(function (item) {\n        if (c.length === 0) {\n          c = c + item.name;\n          cid = cid + item.unitId;\n        } else {\n          c = c + ',' + item.name;\n          cid = cid + ',' + item.unitId;\n        }\n      });\n      this.tableQuery.createOgnName = c;\n      this.deptOrgVisible = false;\n    },\n    add_: function add_(event) {\n      var tableName;\n      var name = this.utils.getDicName(this.utils.compliance_review_type, event);\n      var tabId = this.utils.createUUID();\n      console.log('name:', name);\n      console.log('name.id:', name === null || name === void 0 ? void 0 : name.id);\n      console.log('event:', event);\n      if (name === '重大决策事项合规审查') {\n        tableName = 'hgsc_main_detail';\n      } else if (name === '重要请示事项合规审查') {\n        tableName = 'hgsc_main_detail_qsss';\n      } else if (name === '内部规章制度合法合规审查') {\n        tableName = 'hgsc_main_detail_zdhf';\n      } else if (name === '特殊经营类合规论证') {\n        tableName = 'hgsc_main_detail_tsjy';\n      } else if (name === '合法合规审查意见') {\n        tableName = 'hgsc_main_detail_hfhg';\n      }\n      this.layout.openNewTab(name, tableName, tableName, tabId, _objectSpread(_objectSpread({\n        functionId: tableName + ',' + tabId\n      }, this.utils.routeState.NEW(tabId)), {}, {\n        reviewCategory: event,\n        reviewCategoryName: name\n      }));\n    },\n    // 编辑\n    edit_: function edit_(index, row) {\n      var tableName;\n      if (row.reviewCategoryName === '重大决策事项合规审查') {\n        tableName = 'hgsc_main_detail';\n      } else if (row.reviewCategoryName === '重要请示事项合规审查') {\n        tableName = 'hgsc_main_detail_qsss';\n      } else if (row.reviewCategoryName === '内部规章制度合法合规审查') {\n        tableName = 'hgsc_main_detail_zdhf';\n      } else if (row.reviewCategoryName === '特殊经营类合规论证') {\n        tableName = 'hgsc_main_detail_tsjy';\n      } else if (row.reviewCategoryName === '合法合规审查意见') {\n        tableName = 'hgsc_main_detail_hfhg';\n      }\n      console.log(row, 'row');\n      var tabId = this.utils.createUUID();\n      this.layout.openNewTab(row.reviewCategoryName, tableName, tableName, row.id, _objectSpread(_objectSpread({\n        functionId: tableName + ',' + row.id\n      }, this.utils.routeState.EDIT(row.id)), {}, {\n        view: 'old',\n        reviewCategory: row.reviewCategory,\n        reviewCategoryName: row.reviewCategoryName\n      }));\n    },\n    // 删除\n    delete_: function delete_(index, row) {\n      var _this3 = this;\n      this.$confirm('此操作将永久删除该数据及相关数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        new Promise(function (resolve, reject) {\n          complianceReviewApi.deletebyid({\n            id: row.id\n          }).then(function (response) {\n            resolve(response);\n          });\n        }).then(function (value) {\n          _this3.tableData.splice(index, 1);\n          _this3.$message.success('删除成功!');\n        });\n      }).catch(function () {\n        _this3.$message({\n          type: 'info',\n          message: '已取消删除'\n        });\n      });\n    },\n    // 查看\n    view_: function view_(index, row) {\n      var _this4 = this;\n      var tableName;\n      if (row.reviewCategoryName === '重大决策事项合规审查') {\n        tableName = 'hgsc_main_detail';\n      } else if (row.reviewCategoryName === '重要请示事项合规审查') {\n        tableName = 'hgsc_main_detail_qsss';\n      } else if (row.reviewCategoryName === '内部规章制度合法合规审查') {\n        tableName = 'hgsc_main_detail_zdhf';\n      } else if (row.reviewCategoryName === '特殊经营类合规论证') {\n        tableName = 'hgsc_main_detail_tsjy';\n      } else if (row.reviewCategoryName === '合法合规审查意见') {\n        tableName = 'hgsc_main_detail_hfhg';\n      }\n      if (row.dataStateCode == this.utils.dataState_BPM.SAVE.code) {\n        var tabId = this.utils.createUUID();\n        this.layout.openNewTab('合同合规审查', tableName, tableName, tabId, _objectSpread(_objectSpread({\n          functionId: tableName + ',' + tabId\n        }, this.utils.routeState.VIEW(row.id)), {}, {\n          reviewCategoryName: row.reviewCategoryName\n        }));\n      } else {\n        taskApi.selectTaskId({\n          businessKey: row.id,\n          isView: 'true'\n        }).then(function (res) {\n          var functionId = res.data.data[0].ID;\n          var tabId = _this4.utils.createUUID();\n          _this4.layout.openNewTab('合规审查信息', 'design_page', 'design_page', tabId, {\n            processInstanceId: res.data.data[0].PID,\n            //流程实例\n            taskId: res.data.data[0].ID,\n            //任务ID\n            businessKey: row.id,\n            //业务数据ID\n            functionId: functionId,\n            //不传也可以，只有首环节需要传，实例启动就可以不传了\n            entranceType: 'FLOWABLE',\n            type: 'haveDealt',\n            channel: 'business',\n            //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮\n            view: 'new',\n            reviewCategoryName: row.reviewCategoryName\n          });\n        });\n      }\n    },\n    rowDblclick: function rowDblclick(row, column, event) {\n      var _this5 = this;\n      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code) {\n        var tabId = this.utils.createUUID();\n        this.layout.openNewTab('合规报告审批信息', tableName, tableName, tabId, _objectSpread({\n          functionId: tableName + ',' + tabId\n        }, this.utils.routeState.VIEW(row.id)));\n      } else {\n        taskApi.selectTaskId({\n          businessKey: row.id,\n          isView: 'true'\n        }).then(function (res) {\n          var functionId = res.data.data[0].ID;\n          var tabId = _this5.utils.createUUID();\n          _this5.layout.openNewTab('合规报告审批信息', 'design_page', 'design_page', tabId, {\n            processInstanceId: res.data.data[0].PID,\n            //流程实例\n            taskId: res.data.data[0].ID,\n            //任务ID\n            businessKey: row.id,\n            //业务数据ID\n            functionId: functionId,\n            //不传也可以，只有首环节需要传，实例启动就可以不传了\n            entranceType: 'FLOWABLE',\n            type: 'haveDealt',\n            channel: 'business',\n            //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮\n            view: 'new'\n          });\n        });\n      }\n    },\n    tableSort: function tableSort(column, prop, order) {\n      this.tableQuery.sortName = column.prop;\n      this.tableQuery.order = column.order === 'ascending';\n      this.refreshData();\n    },\n    // 点击搜索按钮事件,回到第一页,重新刷新数据\n    search_: function search_() {\n      this.tableQuery.page = 1;\n      this.refreshData();\n    },\n    // 点击清空按钮事件,清空查询条件属性,回到第一页,重新刷新数据\n    empty_: function empty_() {\n      // 清空搜索条件\n      this.tableQuery = {\n        page: 1,\n        limit: 10,\n        total: 0,\n        riskName: '',\n        caseCode: '',\n        expectedRiskLevel: '',\n        fuzzyValue: '',\n        isQuery: true\n      };\n      this.refreshData();\n    },\n    // 点击刷新按钮事件\n    refresh_: function refresh_() {\n      this.tableQuery.sortName = null;\n      this.tableQuery.order = null;\n      this.empty_();\n    },\n    cancel: function cancel() {\n      this.userDialogVisible = false;\n    },\n    choiceDeptSure: function choiceDeptSure() {\n      var selectedUnits = this.zxcheckedData.map(function (item) {\n        return item.name;\n      }).join(', ');\n      this.tableQuery.reportingUnit = selectedUnits;\n      this.userDialogVisible = false;\n    },\n    exportExcel: function exportExcel() {\n      var _this6 = this;\n      this.exportLoading = true;\n\n      // 显示提示消息\n      var loadingMessage = this.$message({\n        message: '正在准备导出数据，请稍候...',\n        type: 'info',\n        duration: 0\n      });\n      var queryParams = {};\n      Object.assign(queryParams, this.tableQuery);\n      queryParams.limit = 9999;\n      var date = new Date();\n      var formatDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();\n      complianceReviewApi.exportRiskLedger(queryParams).then(function (response) {\n        var blob = response.data;\n        var fileName = formatDate + '合规审查台账.xlsx';\n\n        // 关闭提示消息\n        loadingMessage.close();\n\n        // 下载文件\n        if ('download' in document.createElement('a')) {\n          var elink = document.createElement('a');\n          elink.download = fileName;\n          elink.style.display = 'none';\n          elink.href = URL.createObjectURL(blob);\n          document.body.appendChild(elink);\n          elink.click();\n          URL.revokeObjectURL(elink.href);\n          document.body.removeChild(elink);\n          _this6.$message.success('导出成功');\n        } else {\n          navigator.msSaveBlob(blob, fileName);\n        }\n      }).catch(function (error) {\n        _this6.$message.error('导出失败：' + (error.message || '未知错误'));\n      }).finally(function () {\n        _this6.exportLoading = false;\n      });\n    }\n  }\n};", {"version": 3, "names": ["pagination", "TableTools", "SimpleBoardIndex", "orgTree", "mapGetters", "complianceReviewApi", "taskApi", "name", "inject", "components", "data", "isCheckedUser", "showUser", "deptOrgVisible", "entrustedUnitOrgVisible", "tableQuery", "page", "limit", "total", "reviewSubject", "createOgnName", "reviewCategory", "reviewCategoryName", "submitter", "submissionDateStart", "submissionDateEnd", "fuzzyValue", "dataState", "<PERSON><PERSON><PERSON><PERSON>", "orgId", "businessArea", "table_height", "tableData", "userDialogVisible", "orgDialogVisible", "orgTreeDialog", "zxcheckedData", "orgVisible", "tableLoading", "ss", "tableColumns", "key", "label", "visible", "yearOptions", "Array", "from", "length", "_", "i", "Date", "getFullYear", "businessAreaData", "computed", "_objectSpread", "activated", "refreshData", "created", "initDic", "mounted", "$nextTick", "window", "innerHeight", "$refs", "table", "$el", "offsetTop", "self", "onresize", "methods", "_this", "code", "utils", "getDic", "then", "response", "isEdit", "row", "dataStateCode", "dataState_BPM", "SAVE", "BACK", "isDelete", "_this2", "functionCode", "currentFunctionId", "orgContext", "currentOrgId", "currentPsnFullId", "query", "rows", "records", "catch", "chooseNoticeDeptClick", "deptOrgCancel", "choiceEntrustedUnitClick", "entrustedUnitOrgCancel", "entrustedUnitSure", "res", "console", "log", "choiceNoticeDeptSure", "c", "cid", "for<PERSON>ach", "item", "unitId", "add_", "event", "tableName", "getDicName", "compliance_review_type", "tabId", "createUUID", "id", "layout", "openNewTab", "functionId", "routeState", "NEW", "edit_", "index", "EDIT", "view", "delete_", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "type", "Promise", "resolve", "reject", "deletebyid", "value", "splice", "$message", "success", "message", "view_", "_this4", "VIEW", "selectTaskId", "businessKey", "<PERSON><PERSON><PERSON><PERSON>", "ID", "processInstanceId", "PID", "taskId", "entranceType", "channel", "rowDblclick", "column", "_this5", "tableSort", "prop", "order", "sortName", "search_", "empty_", "riskName", "caseCode", "expectedRiskLevel", "refresh_", "cancel", "choiceDeptSure", "selected<PERSON><PERSON><PERSON>", "map", "join", "reportingUnit", "exportExcel", "_this6", "exportLoading", "loadingMessage", "duration", "queryParams", "Object", "assign", "date", "formatDate", "getMonth", "getDate", "exportRiskLedger", "blob", "fileName", "close", "document", "createElement", "elink", "download", "style", "display", "href", "URL", "createObjectURL", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "navigator", "msSaveBlob", "error", "finally"], "sources": ["src/view/Compliance/Management/ComplianceOperation/ComplianceReport/HgscTz.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"container-manage-sg\" direction=\"vertical\">\r\n    <el-header>\r\n      <el-card>\r\n        <div>\r\n          <el-input v-model=\"tableQuery.fuzzyValue\" class=\"filter_input\" clearable placeholder=\"检索字段（事项题目、审查分类、送审部门）\"\r\n            @clear=\"refreshData\" @keyup.enter.native=\"refreshData\">\r\n            <el-popover slot=\"prepend\" placement=\"bottom-start\" trigger=\"click\" width=\"1000\">\r\n              <el-form ref=\"queryForm\" label-width=\"100px\" size=\"mini\">\r\n                <!-- 选择部门弹窗 -->\r\n                <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"deptOrgVisible\" title=\"选择部门\" width=\"50%\">\r\n                  <div class=\"el-dialog-div\" style=\"z-index: 999999999\">\r\n                    <orgTree :accordion=\"false\" :checked-data.sync=\"zxcheckedData\" :is-check=\"true\"\r\n                      :is-checked-user=\"false\" :is-filter=\"true\" :is-not-cascade=\"true\" :show-user=\"false\" />\r\n                  </div>\r\n                  <span slot=\"footer\" class=\"dialog-footer\">\r\n                    <el-button class=\"negative-btn\" icon=\"\" @click=\"deptOrgCancel\">取消</el-button>\r\n                    <el-button class=\"active-btn\" icon=\"\" type=\"primary\" @click=\"choiceNoticeDeptSure\">确定</el-button>\r\n                  </span>\r\n                </el-dialog>\r\n                <!-- 选择送审人弹窗 -->\r\n                <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"entrustedUnitOrgVisible\" title=\"选择送审人\"\r\n                  width=\"50%\">\r\n                  <div class=\"el-dialog-div\">\r\n                    <orgTree :accordion=\"false\" :checked-data.sync=\"zxcheckedData\" :is-check=\"false\"\r\n                      :is-checked-user=\"false\" :is-filter=\"true\" :is-not-cascade=\"true\" :show-user=\"true\" />\r\n                  </div>\r\n                  <span slot=\"footer\" class=\"dialog-footer\">\r\n                    <el-button class=\"negative-btn\" icon=\"\" @click=\"entrustedUnitOrgCancel\">取消</el-button>\r\n                    <el-button class=\"active-btn\" icon=\"\" type=\"primary\" @click=\"entrustedUnitSure\">确定</el-button>\r\n                  </span>\r\n                </el-dialog>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"送审主题\">\r\n                      <el-input v-model=\"tableQuery.reviewSubject\" clearable placeholder=\"请输入...\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"送审分类\">\r\n                      <el-select v-model=\"tableQuery.reviewCategory\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n                        <el-option v-for=\"item in utils.compliance_report_type\" :key=\"item.dicName\"\r\n                          :label=\"item.dicName\" :value=\"item.dicName\" />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                  <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"审查状态\">\r\n                      <el-input v-model=\"tableQuery.dataState\" clearable placeholder=\"请输入...\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"业务领域\" >\r\n                    <el-select v-model=\"tableQuery.businessArea\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n                      <el-option v-for=\"item in businessAreaData\" :key=\"item.dicName\" :label=\"item.dicName\"\r\n                        :value=\"item.dicName\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <!-- <el-form-item label=\"送审部门\">\r\n                      <el-input v-model=\"tableQuery.createOgnName\" placeholder=\"请选择\" class=\"input-with-select\">\r\n                        <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"showOrgTreeDialog\"/>\r\n                      </el-input>\r\n                    </el-form-item> -->\r\n                    <el-form-item label=\"送审部门\" prop=\"createOgnName\">\r\n                      <el-input v-model=\"tableQuery.createOgnName\" class=\"input-with-select\" clearable\r\n                        placeholder=\"请选择\">\r\n                        <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"chooseNoticeDeptClick\" />\r\n                      </el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"送审人\">\r\n                      <el-input v-model=\"tableQuery.submitter\" class=\"input-with-select\" clearable placeholder=\"请选择\">\r\n                        <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"choiceEntrustedUnitClick\" />\r\n                      </el-input>\r\n                      <!-- <el-input v-model=\"tableQuery.submitter\" placeholder=\"请选择\" class=\"input-with-select\">\r\n                        <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"showUserTreeDialog\"/>\r\n                      </el-input> -->\r\n                      <!-- <span v-else class=\"viewSpan\">{{ mainData.riskDepartment }}</span> -->\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"送审时间\">\r\n                      <el-date-picker v-model=\"tableQuery.submissionDateStart\" clearable placeholder=\"选择日期\"\r\n                        style=\"width: 45%; float: left\" type=\"date\" />\r\n                      <div class=\"label_1\" style=\"width: 10%; float: left; text-align: center\"><span>至</span></div>\r\n                      <el-date-picker v-model=\"tableQuery.submissionDateEnd\" clearable placeholder=\"选择日期\"\r\n                        style=\"width: 45%\" type=\"date\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-button-group style=\"float: right\">\r\n                  <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"search_\">搜索</el-button>\r\n                  <el-button icon=\"el-icon-refresh-left\" size=\"mini\" type=\"primary\" @click=\"empty_\">重置</el-button>\r\n                </el-button-group>\r\n              </el-form>\r\n              <!-- el-dialog 组件 -->\r\n              <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"userDialogVisible\" title=\"选择送审人\" width=\"50%\">\r\n                <div class=\"el-dialog-div\">\r\n                  <orgTree :accordion=\"false\" :checked-data.sync=\"zxcheckedData\" :is-check=\"true\"\r\n                    :is-checked-user=\"isCheckedUser\" :is-filter=\"true\" :is-not-cascade=\"true\" :show-user=\"showUser\" />\r\n                </div>\r\n                <span slot=\"footer\" class=\"dialog-footer\">\r\n                  <el-button class=\"negative-btn\" icon=\"\" @click=\"cancel\">取消</el-button>\r\n                  <el-button class=\"active-btn\" icon=\"\" type=\"primary\" @click=\"choiceDeptSure\">确定</el-button>\r\n                </span>\r\n              </el-dialog>\r\n              <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"orgDialogVisible\" title=\"选择送审部门\" width=\"50%\">\r\n                <div class=\"el-dialog-div\">\r\n                  <orgTree :accordion=\"false\" :checked-data.sync=\"zxcheckedData\" :is-check=\"true\"\r\n                    :is-checked-user=\"false\" :is-filter=\"true\" :is-not-cascade=\"true\" :show-user=\"false\" />\r\n                </div>\r\n                <span slot=\"footer\" class=\"dialog-footer\">\r\n                  <el-button class=\"negative-btn\" icon=\"\" @click=\"cancel\">取消</el-button>\r\n                  <el-button class=\"active-btn\" icon=\"\" type=\"primary\" @click=\"choiceDeptSure\">确定</el-button>\r\n                </span>\r\n              </el-dialog>\r\n              <el-button slot=\"reference\" size=\"small\" type=\"primary\">高级检索</el-button>\r\n            </el-popover>\r\n            <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"search_\" />\r\n          </el-input>\r\n        </div>\r\n      </el-card>\r\n    </el-header>\r\n\r\n    <el-main>\r\n      <!-- 选择送审单位 -->\r\n      <SimpleBoardIndex :title=\"'合规审查台账'\">\r\n        <template slot=\"button\">\r\n          <el-button class=\"normal-btn\" size=\"mini\" type=\"primary\" @click=\"exportExcel\">导出Excel</el-button>\r\n        </template>\r\n        <el-table ref=\"table\" v-loading=\"tableLoading\" :data=\"tableData\" :height=\"table_height\"\r\n          :show-overflow-tooltip=\"true\" border fit highlight-current-row row-key=\"id\" size=\"mini\" stripe\r\n          style=\"table-layout: fixed; width: 100%\" @sort-change=\"tableSort\" @row-dblclick=\"rowDblclick\">\r\n          <el-table-column align=\"center\" label=\"序号\" type=\"index\" width=\"50\" />\r\n          <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'reviewSubject').visible\" label=\"事项题目\"\r\n            min-width=\"250\" prop=\"reviewSubject\" show-overflow-tooltip />\r\n          <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'reviewCategoryName').visible\" label=\"审查分类\"\r\n            min-width=\"180\" prop=\"reviewCategoryName\" show-overflow-tooltip sortable=\"custom\" />\r\n            <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'businessArea').visible\" label=\"业务领域\"\r\n            min-width=\"120\" prop=\"businessArea\" show-overflow-tooltip />\r\n          <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'submitter').visible\" label=\"送审人\"\r\n            min-width=\"100\" prop=\"submitter\" show-overflow-tooltip />\r\n          <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'createOgnName').visible\" label=\"送审单位\"\r\n            min-width=\"250\" prop=\"createOgnName\" show-overflow-tooltip />\r\n          <el-table-column v-if=\"ss.tableColumns.find((item) => item.key === 'submissionDate').visible\" label=\"送审时间\"\r\n            min-width=\"100\" prop=\"submissionDate\" show-overflow-tooltip sortable=\"custom\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.submissionDate | parseTime('{y}-{m}-{d}') }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"审查状态\" min-width=\"100\" prop=\"dataState\" show-overflow-tooltip sortable=\"custom\" />\r\n          <el-table-column align=\"center\" fixed=\"right\" label=\"操作\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-button v-if=\"scope.row.dataState == '已保存'\" type=\"text\" @click=\"edit_(scope.$index, scope.row)\">\r\n                编辑\r\n              </el-button> -->\r\n              <el-button type=\"text\" @click=\"view_(scope.$index, scope.row)\">查看</el-button>\r\n              <!-- <el-button v-if=\"scope.row.dataState == '已保存'\" type=\"text\" @click=\"delete_(scope.$index, scope.row)\">\r\n                删除\r\n              </el-button> -->\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </SimpleBoardIndex>\r\n    </el-main>\r\n    <el-footer>\r\n      <!--分页工具栏-->\r\n      <pagination :limit.sync=\"tableQuery.limit\" :page.sync=\"tableQuery.page\" :total=\"tableQuery.total\"\r\n        @pagination=\"refreshData\" />\r\n    </el-footer>\r\n  </el-container>\r\n</template>\r\n\r\n<script>\r\n// 组件\r\nimport pagination from '@/view/components/Pagination/PaginationIndex';\r\nimport TableTools from '@/view/components/TableTools/index';\r\nimport SimpleBoardIndex from '@/view/components/SimpleBoard/SimpleBoardIndex';\r\nimport orgTree from '@/view/components/OrgTree/OrgTree';\r\n// vuex审查状态值\r\nimport { mapGetters } from 'vuex';\r\n\r\n// 接口api\r\nimport complianceReviewApi from '@/api/ComplianceReview/complianceReview.js';\r\nimport taskApi from '@/api/_system/task';\r\n\r\nexport default {\r\n  name: 'HgscIndex',\r\n  inject: ['layout'],\r\n  components: { pagination, TableTools, SimpleBoardIndex, orgTree },\r\n  data() {\r\n    return {\r\n      isCheckedUser: false,\r\n      showUser: false,\r\n      deptOrgVisible: false,\r\n      entrustedUnitOrgVisible: false,\r\n      // is_Check: false,\r\n      tableQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        total: 0,\r\n        reviewSubject: '', // 事项题目\r\n        createOgnName: '', // 送审单位\r\n        reviewCategory: '', // 审查分类\r\n        reviewCategoryName: '', // 审查分类\r\n        submitter: '', // 送审人\r\n        submissionDateStart: '', // 送审开始时间\r\n        submissionDateEnd: '', // 送审结束时间\r\n        fuzzyValue: '', // 模糊搜索值\r\n        dataState:'',//台账专用---不等于该值\r\n        isQuery:true,//是否查询台账\r\n        orgId: '', // 上报单位id\r\n        businessArea:'',//业务领域\r\n      },\r\n      table_height: '100%',\r\n      tableData: [],\r\n      userDialogVisible: false,\r\n      orgDialogVisible: false,\r\n      orgTreeDialog: false,\r\n      zxcheckedData: [],\r\n      orgVisible: false,\r\n      tableLoading: false,\r\n      ss: {\r\n        data: this.tableData,\r\n        tableColumns: [\r\n          { key: 'reviewSubject', label: '事项题目', visible: true },\r\n          { key: 'reviewCategory', label: '审查分类', visible: true },\r\n          { key: 'reviewCategoryName', label: '审查分类', visible: true },\r\n          { key: 'submissionDate', label: '送审时间', visible: true },\r\n          { key: 'submitter', label: '送审人', visible: true },\r\n          { key: 'createOgnName', label: '送审单位', visible: true },\r\n          { key: 'dataState', label: '审查状态', visible: true },\r\n          { key: 'businessArea', label: '业务领域', visible: true },\r\n        ],\r\n      },\r\n      // tableQuery: {\r\n      //   reportYear: '',\r\n      // },\r\n      yearOptions: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i),\r\n      businessAreaData: [],\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapGetters(['orgContext', 'currentFunctionId']),\r\n  },\r\n  activated() {\r\n    // 长连接页面第二次激活的时候,不会走created方法,会走此方法\r\n    this.refreshData();\r\n  },\r\n  created() {\r\n    this.refreshData();\r\n    this.initDic();\r\n  },\r\n\r\n  mounted() {\r\n    this.$nextTick(function () {\r\n      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45;\r\n\r\n      // 监听窗口大小变化\r\n      const self = this;\r\n      window.onresize = function () {\r\n        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45;\r\n      };\r\n    });\r\n  },\r\n  methods: {\r\n        initDic() {\r\n      const code = ['businessDomainDic'];\r\n      this.utils.getDic(code).then((response) => {\r\n        this.businessAreaData = response.data.data[code[0]];\r\n      })\r\n    },\r\n    isEdit(row) {\r\n      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataStateCode === this.utils.dataState_BPM.BACK.code;\r\n    },\r\n    isDelete(row) {\r\n      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code;\r\n    },\r\n    // 刷新数据\r\n    refreshData() {\r\n      // 赋值当前人组织全路径\r\n      this.tableQuery.functionCode = this.currentFunctionId.functionCode;\r\n      this.tableQuery.orgId = this.orgContext.currentOrgId;\r\n      this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId;\r\n      // 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页\r\n      if (this.tableData.length === 0 && this.tableQuery.page > 1) {\r\n        this.tableQuery.page--;\r\n      }\r\n      complianceReviewApi\r\n        .query(this.tableQuery)\r\n        .then((response) => {\r\n          let rows = response.data.data.records;\r\n          this.tableData = rows;\r\n          this.ss.data = rows;\r\n          this.tableQuery.total = response.data.data.total;\r\n          this.tableLoading = false;\r\n        })\r\n        .catch({});\r\n    },\r\n    // 点击打开查找机构弹窗\r\n    chooseNoticeDeptClick() {\r\n      this.deptOrgVisible = true;\r\n    },\r\n    // 关闭审查机构弹窗\r\n    deptOrgCancel() {\r\n      this.deptOrgVisible = false;\r\n    },\r\n    // 点击打开送审人弹窗\r\n    choiceEntrustedUnitClick() {\r\n      this.entrustedUnitOrgVisible = true;\r\n    },\r\n    // 点击关闭送审人弹窗\r\n    entrustedUnitOrgCancel() {\r\n      this.entrustedUnitOrgVisible = false;\r\n    },\r\n    entrustedUnitSure() {\r\n      const res = this.zxcheckedData[0];\r\n      console.log(res, 'res');\r\n      this.tableQuery.submitter = res.name;\r\n      this.entrustedUnitOrgVisible = false;\r\n    },\r\n    choiceNoticeDeptSure() {\r\n      let c = '';\r\n      let cid = '';\r\n      this.zxcheckedData.forEach((item) => {\r\n        if (c.length === 0) {\r\n          c = c + item.name;\r\n          cid = cid + item.unitId;\r\n        } else {\r\n          c = c + ',' + item.name;\r\n          cid = cid + ',' + item.unitId;\r\n        }\r\n      });\r\n      this.tableQuery.createOgnName = c;\r\n      this.deptOrgVisible = false;\r\n    },\r\n    add_(event) {\r\n      let tableName;\r\n      const name = this.utils.getDicName(this.utils.compliance_review_type, event);\r\n      const tabId = this.utils.createUUID();\r\n      console.log('name:', name);\r\n      console.log('name.id:', name?.id);\r\n      console.log('event:', event);\r\n      if (name ==='重大决策事项合规审查'){\r\n         tableName='hgsc_main_detail';\r\n      }else if (name ==='重要请示事项合规审查'){\r\n         tableName='hgsc_main_detail_qsss';\r\n      }\r\n      else if (name ==='内部规章制度合法合规审查'){\r\n         tableName='hgsc_main_detail_zdhf';\r\n      }\r\n      else if (name ==='特殊经营类合规论证'){\r\n         tableName='hgsc_main_detail_tsjy';\r\n      }\r\n      else if (name ==='合法合规审查意见'){\r\n         tableName='hgsc_main_detail_hfhg';\r\n      }\r\n\r\n      this.layout.openNewTab(name, tableName, tableName, tabId, {\r\n        functionId: tableName +',' + tabId,\r\n        ...this.utils.routeState.NEW(tabId),\r\n        reviewCategory: event, reviewCategoryName: name,\r\n      });\r\n    },\r\n    // 编辑\r\n    edit_(index, row) {\r\n      let tableName;\r\n      if (row.reviewCategoryName ==='重大决策事项合规审查'){\r\n        tableName='hgsc_main_detail';\r\n      }else if (row.reviewCategoryName ==='重要请示事项合规审查'){\r\n        tableName='hgsc_main_detail_qsss';\r\n      }\r\n      else if (row.reviewCategoryName ==='内部规章制度合法合规审查'){\r\n        tableName='hgsc_main_detail_zdhf';\r\n      }\r\n      else if (row.reviewCategoryName ==='特殊经营类合规论证'){\r\n        tableName='hgsc_main_detail_tsjy';\r\n      }\r\n      else if (row.reviewCategoryName ==='合法合规审查意见'){\r\n        tableName='hgsc_main_detail_hfhg';\r\n      }\r\n      console.log(row, 'row');\r\n      const tabId = this.utils.createUUID();\r\n      this.layout.openNewTab(row.reviewCategoryName, tableName, tableName, row.id, {\r\n        functionId: tableName+','  + row.id,\r\n        ...this.utils.routeState.EDIT(row.id),\r\n        view: 'old',\r\n        reviewCategory: row.reviewCategory, reviewCategoryName: row.reviewCategoryName\r\n      });\r\n    },\r\n    // 删除\r\n    delete_(index, row) {\r\n      this.$confirm('此操作将永久删除该数据及相关数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n      })\r\n        .then(() => {\r\n          new Promise((resolve, reject) => {\r\n            complianceReviewApi\r\n              .deletebyid({\r\n                id: row.id,\r\n              })\r\n              .then((response) => {\r\n                resolve(response);\r\n              });\r\n          }).then((value) => {\r\n            this.tableData.splice(index, 1);\r\n            this.$message.success('删除成功!');\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除',\r\n          });\r\n        });\r\n    },\r\n    // 查看\r\n    view_(index, row) {\r\n      let tableName;\r\n      if (row.reviewCategoryName ==='重大决策事项合规审查'){\r\n        tableName='hgsc_main_detail';\r\n      }else if (row.reviewCategoryName ==='重要请示事项合规审查'){\r\n        tableName='hgsc_main_detail_qsss';\r\n      }\r\n      else if (row.reviewCategoryName ==='内部规章制度合法合规审查'){\r\n        tableName='hgsc_main_detail_zdhf';\r\n      }\r\n      else if (row.reviewCategoryName ==='特殊经营类合规论证'){\r\n        tableName='hgsc_main_detail_tsjy';\r\n      }\r\n      else if (row.reviewCategoryName ==='合法合规审查意见'){\r\n        tableName='hgsc_main_detail_hfhg';\r\n      }\r\n      if (row.dataStateCode == this.utils.dataState_BPM.SAVE.code) {\r\n        const tabId = this.utils.createUUID();\r\n        this.layout.openNewTab('合同合规审查', tableName, tableName, tabId, {\r\n          functionId: tableName+',' + tabId,\r\n          ...this.utils.routeState.VIEW(row.id),\r\n          reviewCategoryName: row.reviewCategoryName\r\n        });\r\n      } else {\r\n        taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then((res) => {\r\n          const functionId = res.data.data[0].ID;\r\n          const tabId = this.utils.createUUID();\r\n          this.layout.openNewTab('合规审查信息', 'design_page', 'design_page', tabId, {\r\n            processInstanceId: res.data.data[0].PID, //流程实例\r\n            taskId: res.data.data[0].ID, //任务ID\r\n            businessKey: row.id, //业务数据ID\r\n            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了\r\n            entranceType: 'FLOWABLE',\r\n            type: 'haveDealt',\r\n            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮\r\n            view: 'new',\r\n            reviewCategoryName: row.reviewCategoryName\r\n          });\r\n        });\r\n      }\r\n    },\r\n    rowDblclick(row, column, event) {\r\n      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code) {\r\n        const tabId = this.utils.createUUID();\r\n        this.layout.openNewTab('合规报告审批信息', tableName, tableName, tabId, {\r\n          functionId: tableName+',' + tabId,\r\n          ...this.utils.routeState.VIEW(row.id),\r\n        });\r\n      } else {\r\n        taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then((res) => {\r\n          const functionId = res.data.data[0].ID;\r\n          const tabId = this.utils.createUUID();\r\n          this.layout.openNewTab('合规报告审批信息', 'design_page', 'design_page', tabId, {\r\n            processInstanceId: res.data.data[0].PID, //流程实例\r\n            taskId: res.data.data[0].ID, //任务ID\r\n            businessKey: row.id, //业务数据ID\r\n            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了\r\n            entranceType: 'FLOWABLE',\r\n            type: 'haveDealt',\r\n            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮\r\n            view: 'new',\r\n          });\r\n        });\r\n      }\r\n    },\r\n    tableSort(column, prop, order) {\r\n      this.tableQuery.sortName = column.prop;\r\n      this.tableQuery.order = column.order === 'ascending';\r\n      this.refreshData();\r\n    },\r\n    // 点击搜索按钮事件,回到第一页,重新刷新数据\r\n    search_: function () {\r\n      this.tableQuery.page = 1;\r\n      this.refreshData();\r\n    },\r\n    // 点击清空按钮事件,清空查询条件属性,回到第一页,重新刷新数据\r\n    empty_() {\r\n      // 清空搜索条件\r\n      this.tableQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        total: 0,\r\n        riskName: '',\r\n        caseCode: '',\r\n        expectedRiskLevel: '',\r\n        fuzzyValue: '',\r\n        isQuery: true,\r\n      };\r\n      this.refreshData();\r\n    },\r\n    // 点击刷新按钮事件\r\n    refresh_() {\r\n      this.tableQuery.sortName = null;\r\n      this.tableQuery.order = null;\r\n      this.empty_();\r\n    },\r\n    cancel() {\r\n      this.userDialogVisible = false;\r\n    },\r\n    choiceDeptSure() {\r\n      let selectedUnits = this.zxcheckedData.map((item) => item.name).join(', ');\r\n      this.tableQuery.reportingUnit = selectedUnits;\r\n      this.userDialogVisible = false;\r\n    },\r\n        exportExcel() {\r\n      this.exportLoading = true;\r\n\r\n      // 显示提示消息\r\n      const loadingMessage = this.$message({\r\n        message: '正在准备导出数据，请稍候...',\r\n        type: 'info',\r\n        duration: 0\r\n      });\r\n\r\n      const queryParams = {};\r\n      Object.assign(queryParams, this.tableQuery);\r\n      queryParams.limit = 9999;\r\n\r\n      let date = new Date();\r\n      let formatDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();\r\n\r\n      complianceReviewApi.exportRiskLedger(queryParams).then((response) => {\r\n        const blob = response.data;\r\n        const fileName = formatDate + '合规审查台账.xlsx';\r\n\r\n        // 关闭提示消息\r\n        loadingMessage.close();\r\n\r\n        // 下载文件\r\n        if ('download' in document.createElement('a')) {\r\n          const elink = document.createElement('a');\r\n          elink.download = fileName;\r\n          elink.style.display = 'none';\r\n          elink.href = URL.createObjectURL(blob);\r\n          document.body.appendChild(elink);\r\n          elink.click();\r\n          URL.revokeObjectURL(elink.href);\r\n          document.body.removeChild(elink);\r\n\r\n          this.$message.success('导出成功');\r\n        } else {\r\n          navigator.msSaveBlob(blob, fileName);\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('导出失败：' + (error.message || '未知错误'));\r\n      }).finally(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.el-table__fixed-body-wrapper {\r\n  top: 50px !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AAuLA;AACA,OAAAA,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,OAAA;AACA;AACA,SAAAC,UAAA;;AAEA;AACA,OAAAC,mBAAA;AACA,OAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,MAAA;EACAC,UAAA;IAAAT,UAAA,EAAAA,UAAA;IAAAC,UAAA,EAAAA,UAAA;IAAAC,gBAAA,EAAAA,gBAAA;IAAAC,OAAA,EAAAA;EAAA;EACAO,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,QAAA;MACAC,cAAA;MACAC,uBAAA;MACA;MACAC,UAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,aAAA;QAAA;QACAC,aAAA;QAAA;QACAC,cAAA;QAAA;QACAC,kBAAA;QAAA;QACAC,SAAA;QAAA;QACAC,mBAAA;QAAA;QACAC,iBAAA;QAAA;QACAC,UAAA;QAAA;QACAC,SAAA;QAAA;QACAC,OAAA;QAAA;QACAC,KAAA;QAAA;QACAC,YAAA;MACA;;MACAC,YAAA;MACAC,SAAA;MACAC,iBAAA;MACAC,gBAAA;MACAC,aAAA;MACAC,aAAA;MACAC,UAAA;MACAC,YAAA;MACAC,EAAA;QACA7B,IAAA,OAAAsB,SAAA;QACAQ,YAAA,GACA;UAAAC,GAAA;UAAAC,KAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,GAAA;UAAAC,KAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,GAAA;UAAAC,KAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,GAAA;UAAAC,KAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,GAAA;UAAAC,KAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,GAAA;UAAAC,KAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,GAAA;UAAAC,KAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,GAAA;UAAAC,KAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACA;MACA;MACAC,WAAA,EAAAC,KAAA,CAAAC,IAAA;QAAAC,MAAA;MAAA,aAAAC,CAAA,EAAAC,CAAA;QAAA,WAAAC,IAAA,GAAAC,WAAA,KAAAF,CAAA;MAAA;MACAG,gBAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA,KACAlD,UAAA,sCACA;EACAmD,SAAA,WAAAA,UAAA;IACA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,WAAA;IACA,KAAAE,OAAA;EACA;EAEAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;MACA,KAAA7B,YAAA,GAAA8B,MAAA,CAAAC,WAAA,QAAAC,KAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAC,SAAA;;MAEA;MACA,IAAAC,IAAA;MACAN,MAAA,CAAAO,QAAA;QACAD,IAAA,CAAApC,YAAA,GAAA8B,MAAA,CAAAC,WAAA,GAAAK,IAAA,CAAAJ,KAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAC,SAAA;MACA;IACA;EACA;EACAG,OAAA;IACAX,OAAA,WAAAA,QAAA;MAAA,IAAAY,KAAA;MACA,IAAAC,IAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAF,IAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAAlB,gBAAA,GAAAuB,QAAA,CAAAjE,IAAA,CAAAA,IAAA,CAAA6D,IAAA;MACA;IACA;IACAK,MAAA,WAAAA,OAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,aAAA,UAAAN,KAAA,CAAAO,aAAA,CAAAC,IAAA,CAAAT,IAAA,IAAAM,GAAA,CAAAC,aAAA,UAAAN,KAAA,CAAAO,aAAA,CAAAE,IAAA,CAAAV,IAAA;IACA;IACAW,QAAA,WAAAA,SAAAL,GAAA;MACA,OAAAA,GAAA,CAAAC,aAAA,UAAAN,KAAA,CAAAO,aAAA,CAAAC,IAAA,CAAAT,IAAA;IACA;IACA;IACAf,WAAA,WAAAA,YAAA;MAAA,IAAA2B,MAAA;MACA;MACA,KAAApE,UAAA,CAAAqE,YAAA,QAAAC,iBAAA,CAAAD,YAAA;MACA,KAAArE,UAAA,CAAAc,KAAA,QAAAyD,UAAA,CAAAC,YAAA;MACA,KAAAxE,UAAA,CAAAyE,gBAAA,QAAAF,UAAA,CAAAE,gBAAA;MACA;MACA,SAAAxD,SAAA,CAAAe,MAAA,eAAAhC,UAAA,CAAAC,IAAA;QACA,KAAAD,UAAA,CAAAC,IAAA;MACA;MACAX,mBAAA,CACAoF,KAAA,MAAA1E,UAAA,EACA2D,IAAA,WAAAC,QAAA;QACA,IAAAe,IAAA,GAAAf,QAAA,CAAAjE,IAAA,CAAAA,IAAA,CAAAiF,OAAA;QACAR,MAAA,CAAAnD,SAAA,GAAA0D,IAAA;QACAP,MAAA,CAAA5C,EAAA,CAAA7B,IAAA,GAAAgF,IAAA;QACAP,MAAA,CAAApE,UAAA,CAAAG,KAAA,GAAAyD,QAAA,CAAAjE,IAAA,CAAAA,IAAA,CAAAQ,KAAA;QACAiE,MAAA,CAAA7C,YAAA;MACA,GACAsD,KAAA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAA;MACA,KAAAhF,cAAA;IACA;IACA;IACAiF,aAAA,WAAAA,cAAA;MACA,KAAAjF,cAAA;IACA;IACA;IACAkF,wBAAA,WAAAA,yBAAA;MACA,KAAAjF,uBAAA;IACA;IACA;IACAkF,sBAAA,WAAAA,uBAAA;MACA,KAAAlF,uBAAA;IACA;IACAmF,iBAAA,WAAAA,kBAAA;MACA,IAAAC,GAAA,QAAA9D,aAAA;MACA+D,OAAA,CAAAC,GAAA,CAAAF,GAAA;MACA,KAAAnF,UAAA,CAAAQ,SAAA,GAAA2E,GAAA,CAAA3F,IAAA;MACA,KAAAO,uBAAA;IACA;IACAuF,oBAAA,WAAAA,qBAAA;MACA,IAAAC,CAAA;MACA,IAAAC,GAAA;MACA,KAAAnE,aAAA,CAAAoE,OAAA,WAAAC,IAAA;QACA,IAAAH,CAAA,CAAAvD,MAAA;UACAuD,CAAA,GAAAA,CAAA,GAAAG,IAAA,CAAAlG,IAAA;UACAgG,GAAA,GAAAA,GAAA,GAAAE,IAAA,CAAAC,MAAA;QACA;UACAJ,CAAA,GAAAA,CAAA,SAAAG,IAAA,CAAAlG,IAAA;UACAgG,GAAA,GAAAA,GAAA,SAAAE,IAAA,CAAAC,MAAA;QACA;MACA;MACA,KAAA3F,UAAA,CAAAK,aAAA,GAAAkF,CAAA;MACA,KAAAzF,cAAA;IACA;IACA8F,IAAA,WAAAA,KAAAC,KAAA;MACA,IAAAC,SAAA;MACA,IAAAtG,IAAA,QAAAiE,KAAA,CAAAsC,UAAA,MAAAtC,KAAA,CAAAuC,sBAAA,EAAAH,KAAA;MACA,IAAAI,KAAA,QAAAxC,KAAA,CAAAyC,UAAA;MACAd,OAAA,CAAAC,GAAA,UAAA7F,IAAA;MACA4F,OAAA,CAAAC,GAAA,aAAA7F,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAA2G,EAAA;MACAf,OAAA,CAAAC,GAAA,WAAAQ,KAAA;MACA,IAAArG,IAAA;QACAsG,SAAA;MACA,WAAAtG,IAAA;QACAsG,SAAA;MACA,OACA,IAAAtG,IAAA;QACAsG,SAAA;MACA,OACA,IAAAtG,IAAA;QACAsG,SAAA;MACA,OACA,IAAAtG,IAAA;QACAsG,SAAA;MACA;MAEA,KAAAM,MAAA,CAAAC,UAAA,CAAA7G,IAAA,EAAAsG,SAAA,EAAAA,SAAA,EAAAG,KAAA,EAAA1D,aAAA,CAAAA,aAAA;QACA+D,UAAA,EAAAR,SAAA,SAAAG;MAAA,GACA,KAAAxC,KAAA,CAAA8C,UAAA,CAAAC,GAAA,CAAAP,KAAA;QACA3F,cAAA,EAAAuF,KAAA;QAAAtF,kBAAA,EAAAf;MAAA,EACA;IACA;IACA;IACAiH,KAAA,WAAAA,MAAAC,KAAA,EAAA5C,GAAA;MACA,IAAAgC,SAAA;MACA,IAAAhC,GAAA,CAAAvD,kBAAA;QACAuF,SAAA;MACA,WAAAhC,GAAA,CAAAvD,kBAAA;QACAuF,SAAA;MACA,OACA,IAAAhC,GAAA,CAAAvD,kBAAA;QACAuF,SAAA;MACA,OACA,IAAAhC,GAAA,CAAAvD,kBAAA;QACAuF,SAAA;MACA,OACA,IAAAhC,GAAA,CAAAvD,kBAAA;QACAuF,SAAA;MACA;MACAV,OAAA,CAAAC,GAAA,CAAAvB,GAAA;MACA,IAAAmC,KAAA,QAAAxC,KAAA,CAAAyC,UAAA;MACA,KAAAE,MAAA,CAAAC,UAAA,CAAAvC,GAAA,CAAAvD,kBAAA,EAAAuF,SAAA,EAAAA,SAAA,EAAAhC,GAAA,CAAAqC,EAAA,EAAA5D,aAAA,CAAAA,aAAA;QACA+D,UAAA,EAAAR,SAAA,SAAAhC,GAAA,CAAAqC;MAAA,GACA,KAAA1C,KAAA,CAAA8C,UAAA,CAAAI,IAAA,CAAA7C,GAAA,CAAAqC,EAAA;QACAS,IAAA;QACAtG,cAAA,EAAAwD,GAAA,CAAAxD,cAAA;QAAAC,kBAAA,EAAAuD,GAAA,CAAAvD;MAAA,EACA;IACA;IACA;IACAsG,OAAA,WAAAA,QAAAH,KAAA,EAAA5C,GAAA;MAAA,IAAAgD,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAvD,IAAA;QACA,IAAAwD,OAAA,WAAAC,OAAA,EAAAC,MAAA;UACA/H,mBAAA,CACAgI,UAAA;YACAnB,EAAA,EAAArC,GAAA,CAAAqC;UACA,GACAxC,IAAA,WAAAC,QAAA;YACAwD,OAAA,CAAAxD,QAAA;UACA;QACA,GAAAD,IAAA,WAAA4D,KAAA;UACAT,MAAA,CAAA7F,SAAA,CAAAuG,MAAA,CAAAd,KAAA;UACAI,MAAA,CAAAW,QAAA,CAAAC,OAAA;QACA;MACA,GACA7C,KAAA;QACAiC,MAAA,CAAAW,QAAA;UACAP,IAAA;UACAS,OAAA;QACA;MACA;IACA;IACA;IACAC,KAAA,WAAAA,MAAAlB,KAAA,EAAA5C,GAAA;MAAA,IAAA+D,MAAA;MACA,IAAA/B,SAAA;MACA,IAAAhC,GAAA,CAAAvD,kBAAA;QACAuF,SAAA;MACA,WAAAhC,GAAA,CAAAvD,kBAAA;QACAuF,SAAA;MACA,OACA,IAAAhC,GAAA,CAAAvD,kBAAA;QACAuF,SAAA;MACA,OACA,IAAAhC,GAAA,CAAAvD,kBAAA;QACAuF,SAAA;MACA,OACA,IAAAhC,GAAA,CAAAvD,kBAAA;QACAuF,SAAA;MACA;MACA,IAAAhC,GAAA,CAAAC,aAAA,SAAAN,KAAA,CAAAO,aAAA,CAAAC,IAAA,CAAAT,IAAA;QACA,IAAAyC,KAAA,QAAAxC,KAAA,CAAAyC,UAAA;QACA,KAAAE,MAAA,CAAAC,UAAA,WAAAP,SAAA,EAAAA,SAAA,EAAAG,KAAA,EAAA1D,aAAA,CAAAA,aAAA;UACA+D,UAAA,EAAAR,SAAA,SAAAG;QAAA,GACA,KAAAxC,KAAA,CAAA8C,UAAA,CAAAuB,IAAA,CAAAhE,GAAA,CAAAqC,EAAA;UACA5F,kBAAA,EAAAuD,GAAA,CAAAvD;QAAA,EACA;MACA;QACAhB,OAAA,CAAAwI,YAAA;UAAAC,WAAA,EAAAlE,GAAA,CAAAqC,EAAA;UAAA8B,MAAA;QAAA,GAAAtE,IAAA,WAAAwB,GAAA;UACA,IAAAmB,UAAA,GAAAnB,GAAA,CAAAxF,IAAA,CAAAA,IAAA,IAAAuI,EAAA;UACA,IAAAjC,KAAA,GAAA4B,MAAA,CAAApE,KAAA,CAAAyC,UAAA;UACA2B,MAAA,CAAAzB,MAAA,CAAAC,UAAA,yCAAAJ,KAAA;YACAkC,iBAAA,EAAAhD,GAAA,CAAAxF,IAAA,CAAAA,IAAA,IAAAyI,GAAA;YAAA;YACAC,MAAA,EAAAlD,GAAA,CAAAxF,IAAA,CAAAA,IAAA,IAAAuI,EAAA;YAAA;YACAF,WAAA,EAAAlE,GAAA,CAAAqC,EAAA;YAAA;YACAG,UAAA,EAAAA,UAAA;YAAA;YACAgC,YAAA;YACApB,IAAA;YACAqB,OAAA;YAAA;YACA3B,IAAA;YACArG,kBAAA,EAAAuD,GAAA,CAAAvD;UACA;QACA;MACA;IACA;IACAiI,WAAA,WAAAA,YAAA1E,GAAA,EAAA2E,MAAA,EAAA5C,KAAA;MAAA,IAAA6C,MAAA;MACA,IAAA5E,GAAA,CAAAC,aAAA,UAAAN,KAAA,CAAAO,aAAA,CAAAC,IAAA,CAAAT,IAAA;QACA,IAAAyC,KAAA,QAAAxC,KAAA,CAAAyC,UAAA;QACA,KAAAE,MAAA,CAAAC,UAAA,aAAAP,SAAA,EAAAA,SAAA,EAAAG,KAAA,EAAA1D,aAAA;UACA+D,UAAA,EAAAR,SAAA,SAAAG;QAAA,GACA,KAAAxC,KAAA,CAAA8C,UAAA,CAAAuB,IAAA,CAAAhE,GAAA,CAAAqC,EAAA,EACA;MACA;QACA5G,OAAA,CAAAwI,YAAA;UAAAC,WAAA,EAAAlE,GAAA,CAAAqC,EAAA;UAAA8B,MAAA;QAAA,GAAAtE,IAAA,WAAAwB,GAAA;UACA,IAAAmB,UAAA,GAAAnB,GAAA,CAAAxF,IAAA,CAAAA,IAAA,IAAAuI,EAAA;UACA,IAAAjC,KAAA,GAAAyC,MAAA,CAAAjF,KAAA,CAAAyC,UAAA;UACAwC,MAAA,CAAAtC,MAAA,CAAAC,UAAA,2CAAAJ,KAAA;YACAkC,iBAAA,EAAAhD,GAAA,CAAAxF,IAAA,CAAAA,IAAA,IAAAyI,GAAA;YAAA;YACAC,MAAA,EAAAlD,GAAA,CAAAxF,IAAA,CAAAA,IAAA,IAAAuI,EAAA;YAAA;YACAF,WAAA,EAAAlE,GAAA,CAAAqC,EAAA;YAAA;YACAG,UAAA,EAAAA,UAAA;YAAA;YACAgC,YAAA;YACApB,IAAA;YACAqB,OAAA;YAAA;YACA3B,IAAA;UACA;QACA;MACA;IACA;IACA+B,SAAA,WAAAA,UAAAF,MAAA,EAAAG,IAAA,EAAAC,KAAA;MACA,KAAA7I,UAAA,CAAA8I,QAAA,GAAAL,MAAA,CAAAG,IAAA;MACA,KAAA5I,UAAA,CAAA6I,KAAA,GAAAJ,MAAA,CAAAI,KAAA;MACA,KAAApG,WAAA;IACA;IACA;IACAsG,OAAA,WAAAA,QAAA;MACA,KAAA/I,UAAA,CAAAC,IAAA;MACA,KAAAwC,WAAA;IACA;IACA;IACAuG,MAAA,WAAAA,OAAA;MACA;MACA,KAAAhJ,UAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACA8I,QAAA;QACAC,QAAA;QACAC,iBAAA;QACAxI,UAAA;QACAE,OAAA;MACA;MACA,KAAA4B,WAAA;IACA;IACA;IACA2G,QAAA,WAAAA,SAAA;MACA,KAAApJ,UAAA,CAAA8I,QAAA;MACA,KAAA9I,UAAA,CAAA6I,KAAA;MACA,KAAAG,MAAA;IACA;IACAK,MAAA,WAAAA,OAAA;MACA,KAAAnI,iBAAA;IACA;IACAoI,cAAA,WAAAA,eAAA;MACA,IAAAC,aAAA,QAAAlI,aAAA,CAAAmI,GAAA,WAAA9D,IAAA;QAAA,OAAAA,IAAA,CAAAlG,IAAA;MAAA,GAAAiK,IAAA;MACA,KAAAzJ,UAAA,CAAA0J,aAAA,GAAAH,aAAA;MACA,KAAArI,iBAAA;IACA;IACAyI,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,aAAA;;MAEA;MACA,IAAAC,cAAA,QAAArC,QAAA;QACAE,OAAA;QACAT,IAAA;QACA6C,QAAA;MACA;MAEA,IAAAC,WAAA;MACAC,MAAA,CAAAC,MAAA,CAAAF,WAAA,OAAAhK,UAAA;MACAgK,WAAA,CAAA9J,KAAA;MAEA,IAAAiK,IAAA,OAAAhI,IAAA;MACA,IAAAiI,UAAA,GAAAD,IAAA,CAAA/H,WAAA,YAAA+H,IAAA,CAAAE,QAAA,gBAAAF,IAAA,CAAAG,OAAA;MAEAhL,mBAAA,CAAAiL,gBAAA,CAAAP,WAAA,EAAArG,IAAA,WAAAC,QAAA;QACA,IAAA4G,IAAA,GAAA5G,QAAA,CAAAjE,IAAA;QACA,IAAA8K,QAAA,GAAAL,UAAA;;QAEA;QACAN,cAAA,CAAAY,KAAA;;QAEA;QACA,kBAAAC,QAAA,CAAAC,aAAA;UACA,IAAAC,KAAA,GAAAF,QAAA,CAAAC,aAAA;UACAC,KAAA,CAAAC,QAAA,GAAAL,QAAA;UACAI,KAAA,CAAAE,KAAA,CAAAC,OAAA;UACAH,KAAA,CAAAI,IAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAX,IAAA;UACAG,QAAA,CAAAS,IAAA,CAAAC,WAAA,CAAAR,KAAA;UACAA,KAAA,CAAAS,KAAA;UACAJ,GAAA,CAAAK,eAAA,CAAAV,KAAA,CAAAI,IAAA;UACAN,QAAA,CAAAS,IAAA,CAAAI,WAAA,CAAAX,KAAA;UAEAjB,MAAA,CAAAnC,QAAA,CAAAC,OAAA;QACA;UACA+D,SAAA,CAAAC,UAAA,CAAAlB,IAAA,EAAAC,QAAA;QACA;MACA,GAAA5F,KAAA,WAAA8G,KAAA;QACA/B,MAAA,CAAAnC,QAAA,CAAAkE,KAAA,YAAAA,KAAA,CAAAhE,OAAA;MACA,GAAAiE,OAAA;QACAhC,MAAA,CAAAC,aAAA;MACA;IACA;EACA;AACA"}]}