package com.klaw.controller.complianceRiskController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.klaw.entity.complianceReviewBean.ComplianceReview;
import com.klaw.service.complianceReviewService.ComplianceReviewService;
import com.klaw.vo.Json;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RuntimeService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: SunHao
 * @date: 2024/11/27 下午4:38
 */
@Slf4j
@RestController
@RequestMapping("/ComplianceReview")
public class ComplianceReviewController {

    @Resource
    private ComplianceReviewService complianceReviewService;
    @Resource
    private RuntimeService runtimeService;

    @RequestMapping("/query")
    public Json query(@RequestBody String body) {
        String oper = "ComplianceReview-query";
        JSONObject jsonObject = JSON.parseObject(body);
        return Json.succ(oper, complianceReviewService.queryPageData(jsonObject));
    }

    @RequestMapping("/queryById")
    public Json queryById(@RequestBody String body) {
        String oper = "ComplianceReview-queryById";
        JSONObject jsonObject = JSON.parseObject(body);
        return Json.succ(oper, complianceReviewService.queryDataById(jsonObject.getString("id")));
    }

    @RequestMapping("/getOriginalComplianceReview")
    public Json getOriginalComplianceReview(@RequestBody String body) {
        String oper = "ComplianceReview-getOriginalComplianceReview";
        JSONObject jsonObject = JSON.parseObject(body);
        return Json.succ(oper, complianceReviewService.getOriginalComplianceReview(jsonObject));
    }

    /**
     * 关联重大事项合规审查弹窗数据
     * @param body
     * @return
     */
    @RequestMapping("/queryMajorMatter")
    public Json queryMajorMatter(@RequestBody String body) {
        String oper = "ComplianceReview-queryMajorIssues";
        JSONObject jsonObject = JSON.parseObject(body);
        return Json.succ(oper, complianceReviewService.queryMajorMatter(jsonObject));
    }

    @RequestMapping("/save")
    public Json save(@RequestBody String body) {
        String oper = "ComplianceReview-save";
        ComplianceReview complianceReview = JSONObject.parseObject(body, ComplianceReview.class);
        complianceReviewService.saveData(complianceReview);
        return Json.succ(oper);
    }

    @RequestMapping("/deleteById")
    public Json deleteById(@RequestBody String body) {
        String oper = "ComplianceReview-deleteById";
        JSONObject jsonObject = JSON.parseObject(body);
        return Json.succ(oper, complianceReviewService.removeById(jsonObject.getString("id")));
    }

    @PostMapping("/setParam")
    public Json setParam(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        String id = jsonObject.getString("businessKey");
        String processInstanceId = jsonObject.getString("processInstanceId");

        ComplianceReview complianceReview = complianceReviewService.getById(id);
        Map<String, Object> map = new HashMap<>();
        map.put("ComplianceReview", complianceReview);

        runtimeService.setVariables(processInstanceId, map);
        return Json.succ().data("");
    }

    @PostMapping("/exportRiskLedger")
    public void exportRiskLedger(@RequestBody String body, HttpServletResponse response) throws Exception {
        log.info("{} body: {}", "/exportRiskLedger", body);
        JSONObject json = JSON.parseObject(body);
        complianceReviewService.exportRiskLedger(json, response);
    }
}
