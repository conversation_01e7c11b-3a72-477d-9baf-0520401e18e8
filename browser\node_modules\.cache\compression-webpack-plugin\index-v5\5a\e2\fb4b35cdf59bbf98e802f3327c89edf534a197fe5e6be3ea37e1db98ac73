
292a6431119dbd2223e6112e903163ed4af13628	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fapp.1756902943813.js\",\"contentHash\":\"68cb47a25f3bce514df2ecc14d062a96\"}","integrity":"sha512-qCVkjkgAIN9NsyqIm2wePbgdVJTaTmVBi/PQMe7akvXfHd57eJ3yhBAxtzBKgPkbMtU5a2I18isXINhxCeSnbw==","time":1756904672705,"size":60603571}