package com.klaw.service.complianceReviewService;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.klaw.entity.complianceReviewBean.ComplianceReview;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description 针对表【compliance_review(合规审查表)】的数据库操作Service
 * @createDate 2024-11-26 18:23:57
 */
public interface ComplianceReviewService extends IService<ComplianceReview> {

    Page<ComplianceReview> queryPageData(JSONObject jsonObject);

    Page<ComplianceReview> queryMajorMatter(JSONObject jsonObject);

    void saveData(ComplianceReview complianceReview);

    Page<ComplianceReview> getOriginalComplianceReview(JSONObject jsonObject);

    ComplianceReview queryDataById(String id);

    /**
     * 合规审查台账导出
     * @param json 查询条件
     * @param response HTTP响应对象，用于返回Excel文件
     */
    void exportRiskLedger(JSONObject json, HttpServletResponse response);
}
